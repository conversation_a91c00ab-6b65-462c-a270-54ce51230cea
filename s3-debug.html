<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3配置调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, button { padding: 8px; margin: 5px 0; }
        input[type="text"], input[type="password"] { width: 400px; }
        button { background: #007acc; color: white; border: none; cursor: pointer; border-radius: 4px; padding: 10px 20px; }
        button:hover { background: #005a9e; }
        .result { margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .debug { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
        .tips { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>S3配置调试工具</h1>
        
        <div class="tips">
            <h3>📋 七牛云S3配置说明：</h3>
            <ul>
                <li><strong>服务器地址</strong>: https://s3.cn-south-1.qiniucs.com (不要包含bucket名称)</li>
                <li><strong>访问密钥</strong>: 你的Access Key</li>
                <li><strong>秘密密钥</strong>: 你的Secret Key</li>
                <li><strong>存储桶名称</strong>: 你的bucket名称</li>
                <li><strong>区域</strong>: cn-south-1</li>
            </ul>
        </div>
        
        <div class="form-group">
            <label for="server">服务器地址:</label>
            <input type="text" id="server" value="https://s3.cn-south-1.qiniucs.com">
        </div>
        
        <div class="form-group">
            <label for="accessKey">访问密钥 (Access Key):</label>
            <input type="text" id="accessKey" placeholder="输入你的Access Key">
        </div>
        
        <div class="form-group">
            <label for="secretKey">秘密密钥 (Secret Key):</label>
            <input type="password" id="secretKey" placeholder="输入你的Secret Key">
        </div>
        
        <div class="form-group">
            <label for="bucket">存储桶名称:</label>
            <input type="text" id="bucket" value="siyuan-mediaplayer">
        </div>
        
        <div class="form-group">
            <label for="region">区域:</label>
            <input type="text" id="region" value="cn-south-1">
        </div>
        
        <button onclick="debugConnection()">🔍 调试连接</button>
        <button onclick="testUrl()">🌐 测试URL构建</button>
        
        <div id="result" class="result" style="display: none;"></div>
        <div id="debug" class="debug" style="display: none;"></div>
    </div>

    <script>
        function getConfig() {
            return {
                server: document.getElementById('server').value.trim(),
                accessKey: document.getElementById('accessKey').value.trim(),
                secretKey: document.getElementById('secretKey').value.trim(),
                bucket: document.getElementById('bucket').value.trim(),
                region: document.getElementById('region').value.trim()
            };
        }
        
        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.className = `result ${type}`;
            result.innerHTML = message;
        }
        
        function showDebug(content) {
            const debug = document.getElementById('debug');
            debug.style.display = 'block';
            debug.textContent = content;
        }
        
        async function sha256(message) {
            const hashBuffer = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(message));
            return Array.from(new Uint8Array(hashBuffer)).map(b => b.toString(16).padStart(2, '0')).join('');
        }

        async function hmacSha256(key, message) {
            const cryptoKey = await crypto.subtle.importKey('raw', key, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
            const signature = await crypto.subtle.sign('HMAC', cryptoKey, new TextEncoder().encode(message));
            return Array.from(new Uint8Array(signature)).map(b => b.toString(16).padStart(2, '0')).join('');
        }

        async function hmacSha256Raw(key, message) {
            const cryptoKey = await crypto.subtle.importKey('raw', key, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
            const signature = await crypto.subtle.sign('HMAC', cryptoKey, new TextEncoder().encode(message));
            return new Uint8Array(signature);
        }

        async function getSignatureKey(key, dateStamp, region) {
            const kDate = await hmacSha256Raw(new TextEncoder().encode('AWS4' + key), dateStamp);
            const kRegion = await hmacSha256Raw(kDate, region);
            const kService = await hmacSha256Raw(kRegion, 's3');
            return await hmacSha256Raw(kService, 'aws4_request');
        }
        
        async function generateS3Headers(method, path, config) {
            const now = new Date();
            const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
            const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
            const region = config.region || 'us-east-1';

            // 解析主机名
            const url = new URL(config.server);
            const host = `${config.bucket}.${url.hostname}`;
            
            // 规范化请求
            const canonicalUri = path.split('?')[0] || '/';
            const canonicalQueryString = path.includes('?') ? path.split('?')[1] : '';
            const canonicalHeaders = `host:${host}\nx-amz-content-sha256:UNSIGNED-PAYLOAD\nx-amz-date:${amzDate}\n`;
            const signedHeaders = 'host;x-amz-content-sha256;x-amz-date';
            const payloadHash = 'UNSIGNED-PAYLOAD';
            
            const canonicalRequest = `${method}\n${canonicalUri}\n${canonicalQueryString}\n${canonicalHeaders}\n${signedHeaders}\n${payloadHash}`;
            
            // 创建签名字符串
            const algorithm = 'AWS4-HMAC-SHA256';
            const credentialScope = `${dateStamp}/${region}/s3/aws4_request`;
            const stringToSign = `${algorithm}\n${amzDate}\n${credentialScope}\n${await sha256(canonicalRequest)}`;
            
            // 计算签名
            const signingKey = await getSignatureKey(config.secretKey, dateStamp, region);
            const signature = await hmacSha256(signingKey, stringToSign);
            
            return {
                headers: [
                    ['Authorization', `${algorithm} Credential=${config.accessKey}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`],
                    ['x-amz-content-sha256', payloadHash],
                    ['x-amz-date', amzDate],
                    ['Host', host]
                ],
                debug: {
                    canonicalRequest,
                    stringToSign,
                    signature,
                    host,
                    url: `${url.protocol}//${host}${path}`
                }
            };
        }
        
        async function debugConnection() {
            const config = getConfig();
            
            if (!config.server || !config.accessKey || !config.secretKey || !config.bucket) {
                showResult('❌ 请填写所有必需字段', 'error');
                return;
            }
            
            try {
                const queryParams = '?list-type=2&max-keys=1';
                const path = `/${queryParams}`;
                
                const { headers, debug } = await generateS3Headers('GET', path, config);
                
                showDebug(`调试信息:
URL: ${debug.url}
Host: ${debug.host}
Canonical Request:
${debug.canonicalRequest}

String to Sign:
${debug.stringToSign}

Signature: ${debug.signature}

Headers:
${headers.map(([k, v]) => `${k}: ${v}`).join('\n')}`);
                
                const response = await fetch(debug.url, {
                    method: 'GET',
                    headers: Object.fromEntries(headers),
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const text = await response.text();
                    showResult('✅ 连接成功！', 'success');
                    console.log('Response:', text);
                } else {
                    const text = await response.text();
                    showResult(`❌ 连接失败: ${response.status} ${response.statusText}<br>响应: ${text}`, 'error');
                }
                
            } catch (error) {
                showResult(`❌ 连接失败: ${error.message}`, 'error');
                console.error('Error:', error);
            }
        }
        
        function testUrl() {
            const config = getConfig();
            
            if (!config.server || !config.bucket) {
                showResult('❌ 请填写服务器地址和存储桶名称', 'error');
                return;
            }
            
            try {
                const url = new URL(config.server);
                const host = `${config.bucket}.${url.hostname}`;
                const testUrl = `${url.protocol}//${host}/`;
                
                showResult(`✅ URL构建测试:<br>
                原始服务器: ${config.server}<br>
                存储桶: ${config.bucket}<br>
                构建的URL: ${testUrl}<br>
                主机名: ${host}`, 'info');
                
            } catch (error) {
                showResult(`❌ URL构建失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
