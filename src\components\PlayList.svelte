<script lang="ts">
    import { createEventDispatcher, onMount } from "svelte";
    import { showMessage, Menu } from "siyuan";
    import Tabs from "./Tabs.svelte";
    import type { MediaItem } from '../core/types';
    import { Media, EXT } from '../core/player';
    import { OpenListManager } from '../core/openlist';
    import { WebDAVManager } from '../core/webdav';
    import { BilibiliParser, isBilibiliAvailable } from '../core/bilibili';
    import { LicenseManager } from '../core/license';
    import { PlayListDatabase } from '../core/PlayListDatabase';

    export let className = '', hidden = false, i18n: any, activeTabId = 'playlist', currentItem: MediaItem | null = null, plugin: any;

    const VIEWS = ['detailed', 'compact', 'grid', 'grid-single'] as const;
    const ICONS = ['M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v2H7V7zm0 4h10v2H7v-2zm0 4h10v2H7v-2z', 'M3 5h18v2H3V5zm0 6h18v2H3v-2zm0 6h18v2H3v-2z', 'M3 3h8v8H3V3zm0 10h8v8H3v-8zm10 0h8v8h-8v-8zm0-10h8v8h-8V3z'];
    const database = new PlayListDatabase(plugin);
    const safe = (fn: Function) => async (...args: any[]) => { try { return await fn(...args); } catch (e: any) { showMessage(e?.message || "操作失败"); } };
    const cfg = async () => await plugin.loadData('config.json') || {};
    const dispatch = createEventDispatcher();

    let state = {
        tab: '目录', view: 'detailed' as typeof VIEWS[number], dbId: '', enabled: false,
        tags: ['目录', '默认'], items: [] as MediaItem[], folder: { type: '', path: '', connected: false },
        edit: '', add: '', exp: new Set<string>(), parts: {} as any, sel: null as MediaItem|null, refs: {} as any,
        drag: { item: -1, tag: '', target: '' }, search: '', searching: false
    };

    const saveView = async () => { const c = await cfg(); c.settings.playlistView = { mode: state.view, tab: state.tab, expanded: [...state.exp] }; await plugin.saveData('config.json', c, 2); };
    const loadView = async () => { const v = (await cfg()).settings?.playlistView; if (v) { state.view = v.mode || 'detailed'; state.tab = v.tab || '目录'; state.exp = new Set(v.expanded || []); } };

    const init = async () => {
        const config = await cfg();
        state.enabled = !!config.settings?.enableDatabase;
        await loadView();
        if (state.enabled) {
            state.dbId = config.settings?.playlistDatabase;
            if (!state.dbId) return showMessage('请在设置中配置播放列表数据库', 0);
            await database.init(state.dbId);
        }
        await load();
    };

    const load = async () => {
        const result = await database.load();
        if (!result) return;
        state.tags = result.tags;
        state.items = state.tab === '目录'
            ? result.tags.filter(t => t !== '目录').map(t => ({ id: `dir-${t}`, title: t, type: 'folder', url: '#', source: 'directory', targetTabId: t, is_dir: true, thumbnail: Media.getThumbnail({ type: 'folder' }) }))
            : result.items.filter(item => item.playlist === state.tab);
    };

    const add = async (url: string, playlist = '默认', checkDup = true) => { await database.add(url, playlist, checkDup); await load(); window.dispatchEvent(new CustomEvent('refreshPlaylist')); };
    const del = async (title?: string, tagName?: string) => { await database.delete(title, tagName); await load(); };
    const move = async (title: string, newPlaylist: string) => { await database.move(title, newPlaylist); await load(); };
    const deleteTag = async (tagName: string) => { await database.deleteTag(tagName); await load(); };
    const renameTag = async (oldName: string, newName: string) => { await database.renameTag(oldName, newName); await load(); };

    const browse = async (type: string, path = '') => {
        const managers = { openlist: OpenListManager, webdav: WebDAVManager };
        if (managers[type]) {
            const items = await managers[type].createMediaItemsFromDirectory(path || '/');
            state.folder = { type, path: path || '/', connected: true };
            if (state.tab === (type === 'openlist' ? 'OpenList' : 'WebDAV')) state.items = Array.isArray(items) ? items : [];
        } else {
            if (!window.navigator.userAgent.includes('Electron') || typeof window.require !== 'function') throw new Error('此功能仅在桌面版可用');
            const fs = window.require('fs'), pathLib = window.require('path');
            const fullPath = type === 'siyuan' ? pathLib.join(window.siyuan.config.system.workspaceDir, 'data', path) : path;
            const items: any[] = [];
            try {
                fs.readdirSync(fullPath).forEach((file: string) => {
                    const filePath = pathLib.join(fullPath, file), stats = fs.statSync(filePath);
                    if (stats.isDirectory()) {
                        items.push({ id: `${type}-folder-${Date.now()}-${Math.random().toString(36).slice(2,5)}`, title: file, type: 'folder', url: '#', source: type, sourcePath: type === 'siyuan' ? pathLib.relative(pathLib.join(window.siyuan.config.system.workspaceDir, 'data'), filePath).replace(/\\/g, '/') : filePath, is_dir: true });
                    } else if (EXT.MEDIA.some(ext => file.toLowerCase().endsWith(ext))) {
                        const relativePath = type === 'siyuan' ? pathLib.relative(pathLib.join(window.siyuan.config.system.workspaceDir, 'data'), filePath).replace(/\\/g, '/') : filePath;
                        items.push({ id: `${type}-${Date.now()}-${Math.floor(Math.random() * 1000)}`, title: file, url: `file://${filePath.replace(/\\/g, '/')}`, originalUrl: type === 'siyuan' ? relativePath : undefined, type: EXT.AUDIO.includes(`.${file.toLowerCase().split('.').pop()}`) ? 'audio' : 'video', source: '本地', sourcePath: filePath });
                    }
                });
            } catch (error) { showMessage('扫描文件夹失败'); }
            state.folder = { type, path: path || '', connected: true };
            if ((type === 'siyuan' && state.tab === '思源空间') || type === 'folder') state.items = items;
        }
    };

    $: paths = state.folder.path.split('/').filter(Boolean).map((p, i, arr) => ({ name: p, path: ((state.folder.type === 'openlist' || state.folder.type === 'webdav') ? '/' : '') + arr.slice(0, i + 1).join('/') }));
    $: items = state.items;
    $: hasDir = state.items.some(i => i?.is_dir);
    $: playing = (item: MediaItem) => currentItem?.id === item.id || currentItem?.id?.startsWith(`${item.id}-p`);
    $: selected = (item: MediaItem) => state.sel?.id === item.id;
    $: isGrid = state.view.includes('grid');
    $: isCompact = state.view === 'compact';

    const srcs = { 'bilibili': 'B站', 'local': '本地', 'standard': '普通', 'openlist': 'OpenList', 'webdav': 'WebDAV', 'directory': '标签', 'siyuan': '思源' };
    const tags = (item: MediaItem) => `<span class="meta-tag source" data-source="${item.source === 'directory' ? '标签' : item.source === 'siyuan' ? '思源' : srcs[item.source] || item.source}">${item.source === 'directory' ? '标签' : item.source === 'siyuan' ? '思源' : srcs[item.source] || item.source}</span><span class="meta-tag type" data-type="${item.type === 'audio' ? '音频' : item.type === 'folder' ? '文件夹' : '视频'}">${item.type === 'audio' ? '音频' : item.type === 'folder' ? '文件夹' : '视频'}</span>`;
    const map = (m: any, k: string) => m[k] || k;
    const tabs = { '目录': i18n?.playList?.tabs?.directory, '默认': i18n?.playList?.tabs?.default };
    const nextView = () => { state.view = VIEWS[(VIEWS.indexOf(state.view) + 1) % 4]; saveView(); };
    const setTab = async (tag: string) => { if (tag === state.tab) return; state.tab = tag; state.searching = false; state.search = ''; await load(); saveView(); if (tag === 'OpenList' && !state.items.length) await connect('openlist', 'OpenList', '/'); else if (tag === 'WebDAV' && !state.items.length) await connect('webdav', 'WebDAV', '/'); else if (tag === '思源空间' && !state.items.length) await browse('siyuan', ''); else if (!['OpenList', 'WebDAV', '思源空间'].includes(tag)) state.folder = { type: '', path: '', connected: false }; };
    const searchAll = async () => {
        if (!state.search.trim()) return state.items = [];
        const query = state.search.toLowerCase(), allItems = [];
        for (const tag of state.tags.filter(t => t !== '目录' && t !== '搜索')) {
            const oldTab = state.tab; state.tab = tag; await load(); allItems.push(...state.items); state.tab = oldTab;
        }
        state.items = allItems.filter(item => item.title?.toLowerCase().includes(query) || item.artist?.toLowerCase().includes(query) || item.url?.toLowerCase().includes(query));
    };

    const connect = async (type: string, tag: string, path = '') => {
        const managers = { openlist: OpenListManager, webdav: WebDAVManager };
        if (managers[type] && (state.folder.type !== type || !state.folder.connected) && !await managers[type].initFromConfig(await cfg())) {
            return showMessage(`请先配置${type === 'openlist' ? 'OpenList' : 'WebDAV'}连接`);
        }
        if (managers[type]) state.folder = { connected: true, type, path: '' };
        if (!state.tags.includes(tag)) { await database.ensureTag(tag); await load(); }
        state.tab = tag; await browse(type, path);
    };

    const click = safe(async (item: MediaItem) => {
        state.sel = item;
        if (item.source === 'directory') return setTab(item.title);
        if (item.is_dir) return play(item);
        const bvid = item.bvid || item.url?.match(/BV[a-zA-Z0-9]+/)?.[0];
        if (bvid && !state.parts[item.id] && isBilibiliAvailable()) state.parts[item.id] = await BilibiliParser.getVideoParts({ bvid }) || [];
        if (state.parts[item.id]?.length > 1) { state.exp = new Set(state.exp.has(item.id) ? [...state.exp].filter(id => id !== item.id) : [...state.exp, item.id]); saveView(); }
    });

    const play = safe(async (item: MediaItem, startTime?: number, endTime?: number) => {
        if (item.source === 'directory' && item.targetTabId) { state.tab = item.targetTabId; return load(); }
        if (item.is_dir) return browse(item.source === 'openlist' ? 'openlist' : item.source === 'webdav' ? 'webdav' : item.source === 'siyuan' ? 'siyuan' : 'folder', item.sourcePath || '');
        const managers = { openlist: OpenListManager, webdav: WebDAVManager };
        if (managers[item.source] && item.sourcePath && !item.is_dir) return dispatch('play', await managers[item.source].createMediaItemFromPath(item.sourcePath));

        const config = await cfg(), opts = { ...item, type: item.type || 'video', startTime, endTime };
        const bvid = item.bvid || item.url?.match(/BV[a-zA-Z0-9]+/)?.[0];
        if ((item.source === 'B站' || item.type === 'bilibili') && bvid) {
            if (!isBilibiliAvailable()) return;
            if (!config.settings?.bilibiliLogin?.mid) return showMessage('需要登录B站才能播放视频');
            const cid = item.cid || (await BilibiliParser.getVideoParts({ bvid }))?.[0]?.cid;
            if (cid) Object.assign(opts, { url: await BilibiliParser.getProcessedVideoStream(bvid, cid, 0, config), originalUrl: item.originalUrl || item.url, type: 'video', bvid, cid });
        }
        currentItem = item; dispatch('play', opts);
    });

    const dragStart = (type, i) => (state.drag = type === 'item' ? { item: i, tag: '', target: '' } : { item: -1, tag: i, target: '' });
    const dragEnter = (e, type, i) => (e.preventDefault(), type === 'item' && state.drag.item !== i && state.drag.item > -1 && ([state.items[state.drag.item], state.items[i]] = [state.items[i], state.items[state.drag.item]], state.drag.item = i), type === 'tag' && state.drag.item > -1 && (state.drag.target = i), type === 'tag' && state.drag.tag && state.drag.tag !== i && state.tags.splice(state.tags.indexOf(i), 0, state.tags.splice(state.tags.indexOf(state.drag.tag), 1)[0]));
    const dragEnd = async () => {
        if (state.drag.item > -1 && state.drag.target) await move(state.items[state.drag.item].title, state.drag.target);
        state.drag = { item: -1, tag: '', target: '' };
    };

    const openExternal = (item: MediaItem) => window.require?.('electron').shell[item.source === 'local' || (item.originalUrl || item.url).startsWith('file://') ? 'showItemInFolder' : 'openExternal']((item.originalUrl || item.url).replace('file://', ''));
    const menus = {
        media: (item: any) => [["iconPlay", "播放", () => play(item)], ["iconLink", "外部打开", () => openExternal(item)], ...(state.tags.filter(t => t !== state.tab && t !== '目录').length ? [["iconMove", "移动到", state.tags.filter(t => t !== state.tab && t !== '目录').map(t => [t, () => move(item.title, t)])]] : []), ["iconTrashcan", "删除", () => del(item.title)]],
        tab: (tag: any) => [...(tag === '默认' || tag === '目录' ? [] : [["iconEdit", "重命名", () => { state.edit = tag; setTimeout(() => state.refs.edit?.focus(), 0); }], ["iconRefresh", "刷新", () => refreshTag(tag)]]), ["iconClear", "清空", () => del(undefined, state.tab)], ...(tag === '默认' || tag === '目录' ? [] : [["iconTrashcan", "删除", () => delTag(tag)]])],
        add: async (_, e: MouseEvent) => {
            const isPro = (await LicenseManager.load(plugin).catch(() => null))?.isValid && ['annual', 'dragon'].includes((await LicenseManager.load(plugin).catch(() => null))?.type);
            const getAccounts = async (type) => ((await cfg()).settings?.[`${type}Accounts`] || []).map(acc => [
                type === 'bilibili' ? acc.uname || acc.mid : new URL(acc.server).hostname.replace('www.', ''),
                type === 'bilibili' ? async () => {
                    if (!isBilibiliAvailable()) return;
                    const c = await cfg(), o = c.settings?.bilibiliLogin;
                    c.settings.bilibiliLogin = acc; await plugin.saveData('config.json', c, 2);
                    try {
                        const folders = await BilibiliParser.getUserFavoriteFolders(c);
                        if (!folders?.length) return showMessage('未找到收藏夹');
                        const m = new Menu("biliFavs");
                        folders.forEach(f => m.addItem({ icon: "iconHeart", label: `${f.title} (${f.media_count})`, click: async () => { const { items } = await BilibiliParser.getFavoritesList(f.id.toString(), c); await database.ensureTag(f.title, f.id.toString()); await batchAdd(items || [], f.title); } }));
                        m.open({ x: e.clientX, y: e.clientY });
                    } finally { c.settings.bilibiliLogin = o; await plugin.saveData('config.json', c, 2); }
                } : async () => {
                    const mgr = { webdav: WebDAVManager, openlist: OpenListManager }[type];
                    if (mgr && (await mgr.checkConnection(acc)).connected) { mgr.setConfig(acc); await connect(type, `${type === 'webdav' ? 'WebDAV' : 'OpenList'}-${new URL(acc.server).hostname.replace('www.', '')}`, '/'); } else showMessage(`${acc.server} 连接失败`);
                }
            ]);

            return [
                ["iconAdd", "添加新标签页", () => { state.add = 'tag'; setTimeout(() => state.refs.new?.focus(), 50); }],
                ["iconFolder", "添加本地文件夹", () => addFolder()],
                ["iconImage", "添加思源空间", () => connect('siyuan', '思源空间', '')],
                ["iconCloud", "浏览OpenList云盘", isPro ? await getAccounts('openlist') : () => showMessage("此功能需要Pro版本")],
                ["iconCloud", "浏览WebDAV云盘", isPro ? await getAccounts('webdav') : () => showMessage("此功能需要Pro版本")],
                ...(isBilibiliAvailable() ? [["iconHeart", "添加B站收藏夹", isPro ? await getAccounts('bilibili') : () => showMessage("此功能需要Pro版本")], ["iconTags", "添加B站合集", isPro ? () => { state.add = 'season'; setTimeout(() => state.refs.new?.focus(), 50); } : () => showMessage("此功能需要Pro版本")]] : [])
            ];
        }
    };
    const menu = async (e: MouseEvent, type: keyof typeof menus, target?: any) => {
        const m = new Menu(`${type}Menu`), items = await menus[type](target, e);
        items.forEach(([icon, label, action]) => m.addItem(Array.isArray(action) ? { icon, label, submenu: action.map(([l, a]) => ({ label: l, click: a })) } : { icon, label, click: action }));
        m.open({ x: e.clientX, y: e.clientY });
    };

    const batchAdd = async (items: any[], tagName: string, urlFn = (item: any) => item.url || `https://www.bilibili.com/video/${item.bvid}`) => { for (const item of items) try { await add(urlFn(item), tagName, false); } catch {} };

    const addFolder = async () => {
        if (!window.navigator.userAgent.includes('Electron') || typeof window.require !== 'function') return showMessage('此功能仅在桌面版可用');
        const { filePaths } = await window.require('@electron/remote').dialog.showOpenDialog({ properties: ['openDirectory', 'createDirectory'] });
        if (!filePaths?.[0]) return;
        const [folderPath, folderName] = [filePaths[0], filePaths[0].split(/[\\/]/).pop()];
        await database.ensureTag(folderName, folderPath); await browse('folder', folderPath); await batchAdd(state.items.filter(item => !item.is_dir), folderName);
    };

    const addBili = async (e: MouseEvent) => {
        if (!isBilibiliAvailable()) return;
        const config = await cfg();
        if (!config?.settings?.bilibiliLogin?.mid) return showMessage('请先登录B站账号');
        const folders = await BilibiliParser.getUserFavoriteFolders(config);
        if (!folders?.length) return showMessage('未找到收藏夹');
        const biliMenu = new Menu("biliFavs");
        folders.forEach(f => biliMenu.addItem({ icon: "iconHeart", label: `${f.title} (${f.media_count})`, click: async () => { const { items } = await BilibiliParser.getFavoritesList(f.id.toString(), config); await database.ensureTag(f.title, f.id.toString()); await batchAdd(items || [], f.title); } }));
        biliMenu.open({ x: e.clientX, y: e.clientY });
    };

    const addSeason = async (url: string) => {
        if (!isBilibiliAvailable()) return;
        const config = await cfg();
        if (!config?.settings?.bilibiliLogin?.mid) return showMessage('请先登录B站账号');
        const info = await BilibiliParser.getVideoInfo(url) as any;
        if (!info?.seasonId) return showMessage('该视频不属于任何合集');
        const { items } = await BilibiliParser.getSeasonArchives(info.artistId, info.seasonId, config);
        await database.ensureTag(info.seasonTitle || '未命名合集', `${info.artistId}:${info.seasonId}`); await batchAdd(items || [], info.seasonTitle || '未命名合集');
    };

    const refreshTag = async (tagName: string) => showMessage('刷新功能暂未实现');
    const delTag = async (tagName: string) => { if (tagName === '默认') return showMessage('不能删除系统标签'); await deleteTag(tagName); if (state.tab === tagName) state.tab = '默认'; };

    const input = async (e: Event, type: 'tag' | 'add' | 'search', old?: string) => {
        if (e instanceof KeyboardEvent && e.key !== 'Enter') return;
        const value = ((e.target as HTMLInputElement).value || '').trim();
        if (type === 'search') { state.search = value; value ? searchAll() : state.items = []; return; }
        if (!value) return (type === 'tag' ? state.edit = '' : state.add = '');
        if (type === 'tag') { await renameTag(old!, value); state.edit = ''; }
        else if (state.add === 'tag') { state.add = ''; await database.ensureTag(value); await load(); }
        else if (state.add === 'season') { state.add = ''; await addSeason(value); }
    };

    export const playNext = safe(async () => {
        if (!state.items.length || !currentItem || !(await cfg())?.settings?.loopPlaylist) return false;
        const bvid = currentItem.bvid || currentItem.url?.match(/BV[a-zA-Z0-9]+/)?.[0];
        if (bvid && isBilibiliAvailable()) {
            const currentPage = parseInt(currentItem.id?.match(/-p(\d+)/)?.[1] || '1', 10), parts = await BilibiliParser.getVideoParts({ bvid }), nextPart = parts?.find(pt => pt.page === currentPage + 1);
            const config = await cfg();
            if (nextPart || config.settings.loop) {
                const part = nextPart || parts?.find(pt => pt.page === 1);
                if (part) return (Object.assign(currentItem, { id: `${currentItem.id.split('-p')[0]}-p${part.page}`, title: `${currentItem.title.split(' - P')[0]}${part.page > 1 ? ` - P${part.page}${part.part ? ': ' + part.part : ''}` : ''}`, bvid, cid: String(part.cid) }), await play(currentItem), true);
            }
        }
        const currentIndex = state.items.findIndex(i => i.id === currentItem.id || i.url === currentItem.url);
        return currentIndex >= 0 ? (await play(state.items[(currentIndex + 1) % state.items.length]), true) : false;
    });

    const handleAdd = async () => {
        if (state.input.trim()) { try { await add(state.input.trim(), state.tab); state.input = ''; } catch {} }
        else {
            const isDesktop = window.navigator.userAgent.includes('Electron') && typeof window.require === 'function';
            if (isDesktop) {
                const { filePaths } = await window.require('@electron/remote').dialog.showOpenDialog({ properties: ['openFile', 'multiSelections'], filters: [{ name: "媒体文件", extensions: EXT.MEDIA.map(ext => ext.slice(1)) }] });
                const items = filePaths?.map(p => ({ url: `file://${p.replace(/\\/g, '/')}` })) || [];
                items.length > 1 ? await batchAdd(items, state.tab) : items.length && await add(items[0].url, state.tab);
            } else {
                const input = Object.assign(document.createElement('input'), { type: 'file', multiple: true, accept: EXT.MEDIA.join(','), onchange: async (e) => {
                    const files = Array.from((e.target as HTMLInputElement).files || []).map(f => ({ url: URL.createObjectURL(f), title: f.name }));
                    files.length > 1 ? await batchAdd(files, state.tab) : files.length && await add(files[0].url, state.tab);
                }}); input.click();
            }
        }
    };

    onMount(() => { safe(init)(); const handleDataUpdate = () => load(); const handleConfigUpdate = (ev: CustomEvent) => { if (ev.detail?.settings?.enableDatabase !== undefined || ev.detail?.settings?.playlistDb?.id) safe(init)(); }; window.addEventListener('playlist-data-updated', handleDataUpdate); window.addEventListener('configUpdated', handleConfigUpdate); return () => { window.removeEventListener('playlist-data-updated', handleDataUpdate); window.removeEventListener('configUpdated', handleConfigUpdate); }; });

    export { play };
</script>

<div class="panel {className}" class:hidden>
    <!-- 统一导航 -->
    <Tabs {activeTabId} {i18n}>
        <svelte:fragment slot="controls">
            <span class="panel-count">{state.items.length} 项</span>
            <button class="view-mode-btn" on:click={nextView} title="视图">
                <svg viewBox="0 0 24 24" width="16" height="16"><path d={ICONS[VIEWS.indexOf(state.view) % 3]}/></svg>
            </button>
        </svelte:fragment>
    </Tabs>
    
    <!-- 标签 -->
    <div class="panel-tabs">
        <!-- 搜索标签 -->
        {#if state.searching}
            <input bind:this={state.refs.search} type="text" class="tab-input" placeholder="搜索所有媒体..." bind:value={state.search} on:input={e => input(e, 'search')} on:blur={() => !state.search && (state.searching = false)} style="width: 150px;">
        {:else}
            <button class="tab tab-add" on:click={() => (state.searching = true, state.tab = '搜索', setTimeout(() => state.refs.search?.focus(), 0))}>🔍</button>
        {/if}

        {#each state.tags as tag, index (tag)}
            {#if state.edit === tag}
                <input bind:this={state.refs.edit} type="text" class="tab-input" value={tag} on:blur={e => input(e, 'tag', tag)} on:keydown={e => input(e, 'tag', tag)}>
            {:else}
                <button
                    class="tab"
                    class:active={state.tab === tag && !state.searching}
                    draggable={tag !== '目录' && tag !== '默认'}
                    on:click={() => setTab(tag)}
                    on:contextmenu|preventDefault={async e => await menu(e, 'tab', tag)}
                    on:dragstart={() => dragStart('tag', tag)}
                    on:dragover|preventDefault
                    on:dragenter={e => dragEnter(e, 'tag', tag)}
                    on:dragleave={e => state.drag.item !== -1 && (state.drag.target = '')}
                    on:dragend={dragEnd}
                >{map(tabs, tag)}</button>
            {/if}
        {/each}
        {#if state.add}
            <input bind:this={state.refs.new} type="text" class="tab-input" style="width:{state.add === 'season' ? '200px' : '100px'}" placeholder={state.add === 'season' ? (isBilibiliAvailable() ? (i18n.playList?.placeholder?.bilibiliSeason || 'B站合集视频链接') : '扩展未启用') : (i18n.playList?.placeholder?.newTab || '新标签名')} on:blur={e => input(e, 'add')} on:keydown={e => input(e, 'add')}>
        {:else}
            <button class="tab tab-add" on:click|preventDefault|stopPropagation={async e => await menu(e, 'add')}>+</button>
        {/if}
    </div>
    
    <!-- 路径 -->
    {#if state.folder.type && (state.tab === 'OpenList' || state.tab === 'WebDAV' || state.tab === '思源空间') && (hasDir || paths.length)}
            <div class="openlist-path-nav">
        <button class="path-item" on:click={() => browse(state.folder.type, (state.folder.type === 'openlist' || state.folder.type === 'webdav') ? '/' : '')}>根目录</button>
            {#each paths as part, i}
                <span class="path-sep">/</span>
                <button class="path-item" on:click={() => browse(state.folder.type, ((state.folder.type === 'openlist' || state.folder.type === 'webdav') ? '/' : '') + paths.slice(0, i + 1).map(p => p.name).join('/'))}>{part.name}</button>
            {/each}
        </div>
    {/if}
    
    <!-- 内容 -->
    <div class="panel-content" class:grid-view={isGrid}>
        {#if items.length}
            <div class="panel-items" class:grid-single={state.view === 'grid-single'}>
                {#each items as item, index (item.id)}
                   <div class="panel-item"
                        class:playing={playing(item)}
                        class:selected={selected(item)}
                        class:compact={isCompact}
                        class:grid={isGrid}
                        class:folder={item.is_dir}
                        draggable={!item.is_dir}
                        on:click={() => click(item)}
                        on:dblclick={() => play((item.bvid || item.url?.match(/BV[a-zA-Z0-9]+/)?.[0]) && state.parts[item.id]?.length > 1 ? {...item, bvid: item.bvid || item.url?.match(/BV[a-zA-Z0-9]+/)?.[0], cid: String(state.parts[item.id][0].cid)} : item)}
                        on:contextmenu|preventDefault={async e => await menu(e, 'media', item)}
                        on:dragstart={() => dragStart('item', index)}
                        on:dragover|preventDefault
                        on:dragenter={e => dragEnter(e, 'item', index)}
                        on:dragend={dragEnd}>
                        
                        <!-- 极简视图 -->
                        {#if isCompact}
                            <div class="item-title" title={item.title}>{item.title}</div>
                            <div class="item-tags">{@html tags(item)}</div>
                        {:else if isGrid}
                            <div class="item-thumbnail">
                                <img src={Media.getThumbnail(item)} alt={item.title} loading="lazy">
                                <div class="grid-tags">{@html tags(item)}</div>
                                {#if item.duration}<div class="duration">{item.duration}</div>{/if}
                                <div class="item-title" title={item.title}>{item.title}</div>
                            </div>
                        {:else}
                            <div class="item-content">
                                <div class="item-thumbnail">
                                    <img src={Media.getThumbnail(item)} alt={item.title} loading="lazy">
                                    {#if item.duration}<div class="duration">{item.duration}</div>{/if}
                                </div>
                                <div class="item-info">
                                    <div class="item-title" title={item.title}>{item.title}</div>
                                    <div class="item-meta">
                                        {#if item.artist}<div class="item-artist">{#if item.artistIcon}<img class="artist-icon" src={item.artistIcon} alt={item.artist} loading="lazy">{/if}<span>{item.artist}</span></div>{/if}
                                        {@html tags(item)}
                                    </div>
                                    {#if item.url}<div class="item-url" title={item.url}><a href={item.url} target="_blank" rel="noopener noreferrer" on:click|stopPropagation>{item.url}</a></div>{/if}
                                </div>
                            </div>
                        {/if}
                        
                        {#if state.exp.has(item.id) && state.parts[item.id]?.length > 1}
                            <div class="item-parts" class:grid-parts={isGrid && state.view === 'grid'} class:single-parts={state.view === 'grid-single'}>
                                {#each state.parts[item.id] as part}
                                    <button class="part-item {currentItem?.id === `${item.id}-p${part.page}` ? 'playing' : ''}"
                                            on:click|stopPropagation={() => play({...item, id: `${item.id}-p${part.page}`, title: `${item.title.split(' - P')[0]} - P${part.page}${part.part ? ': ' + part.part : ''}`, bvid: item.bvid || item.url?.match(/BV[a-zA-Z0-9]+/)?.[0], cid: String(part.cid)})}
                                            title={part.part || `P${part.page}`}>{part.page}</button>
                                {/each}
                            </div>
                        {/if}
                    </div>
                {/each}
            </div>
        {:else}
            <div class="panel-empty">{!state.enabled ? '请先在设置中启用数据库功能' : !state.dbId ? '请在设置中配置播放列表数据库' : '当前标签暂无媒体项目'}</div>
        {/if}
    </div>

    <!-- 输入 -->
    <div class="panel-footer">
        <input type="text" class="tab-input panel-input" placeholder="输入链接或直接点击添加本地文件..." bind:value={state.input} on:keydown={e => e.key === 'Enter' && handleAdd()} style="padding-right: {state.input ? '25px' : '8px'}">
        {#if state.input}<span style="position:absolute;right:80px;cursor:pointer;color:#666" on:click={() => state.input = ''}>×</span>{/if}
        <button class="add-btn" on:click={handleAdd} title="添加"><svg class="icon"><use xlink:href="#iconAdd"></use></svg></button>
    </div>
</div>