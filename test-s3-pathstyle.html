<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3 Path-Style URL测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .title { font-weight: bold; color: #007acc; margin-bottom: 10px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .comparison-item { padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>S3 Path-Style URL测试</h1>
        
        <div class="section">
            <div class="title">🔧 关键修复：URL格式统一</div>
            <div class="comparison">
                <div class="comparison-item">
                    <h4 class="error">❌ 修复前 (Virtual-Hosted Style)</h4>
                    <pre>
URL: https://bucket.s3.region.qiniucs.com/path
Host: bucket.s3.region.qiniucs.com
签名路径: /path
                    </pre>
                    <p>问题：与tools中的forcePathStyle不一致</p>
                </div>
                <div class="comparison-item">
                    <h4 class="success">✅ 修复后 (Path Style)</h4>
                    <pre>
URL: https://s3.region.qiniucs.com/bucket/path
Host: s3.region.qiniucs.com
签名路径: /bucket/path
                    </pre>
                    <p>修复：与tools中的AWS SDK配置一致</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="title">📊 URL格式对比表</div>
            <table>
                <tr>
                    <th>组件</th>
                    <th>Tools (AWS SDK)</th>
                    <th>修复前</th>
                    <th>修复后</th>
                </tr>
                <tr>
                    <td>URL格式</td>
                    <td>Path Style (forcePathStyle: true)</td>
                    <td>Virtual-Hosted Style</td>
                    <td class="success">✅ Path Style</td>
                </tr>
                <tr>
                    <td>连接测试URL</td>
                    <td>s3.region.qiniucs.com/bucket/?list-type=2&max-keys=1</td>
                    <td>bucket.s3.region.qiniucs.com/?list-type=2&max-keys=1</td>
                    <td class="success">✅ s3.region.qiniucs.com/bucket/?list-type=2&max-keys=1</td>
                </tr>
                <tr>
                    <td>目录列表URL</td>
                    <td>s3.region.qiniucs.com/bucket/?list-type=2&prefix=&delimiter=/</td>
                    <td>bucket.s3.region.qiniucs.com/?list-type=2&prefix=&delimiter=/</td>
                    <td class="success">✅ s3.region.qiniucs.com/bucket/?list-type=2&prefix=&delimiter=/</td>
                </tr>
                <tr>
                    <td>Host头</td>
                    <td>s3.region.qiniucs.com</td>
                    <td>bucket.s3.region.qiniucs.com</td>
                    <td class="success">✅ s3.region.qiniucs.com</td>
                </tr>
                <tr>
                    <td>签名路径</td>
                    <td>/bucket/path</td>
                    <td>/path</td>
                    <td class="success">✅ /bucket/path</td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <div class="title">🎯 修复内容总结</div>
            <ul>
                <li><strong>URL格式</strong>: 从Virtual-Hosted Style改为Path Style</li>
                <li><strong>Host头</strong>: 从bucket.s3.region.qiniucs.com改为s3.region.qiniucs.com</li>
                <li><strong>签名路径</strong>: 包含bucket名称在路径中</li>
                <li><strong>查询参数</strong>: 正确排序和URL编码</li>
                <li><strong>与tools一致</strong>: 完全匹配AWS SDK的forcePathStyle配置</li>
            </ul>
        </div>
        
        <div class="section">
            <div class="title">📋 测试用例</div>
            <div id="testResults">
                <h4>URL构建测试:</h4>
                <div id="urlTests"></div>
            </div>
        </div>
    </div>

    <script>
        function testUrlBuilding() {
            const config = {
                bucket: 'siyuan-playlist',
                region: 'cn-south-1'
            };

            const testCases = [
                {
                    name: '连接测试',
                    path: '?list-type=2&max-keys=1',
                    expectedUrl: `https://s3.${config.region}.qiniucs.com/${config.bucket}/?list-type=2&max-keys=1`,
                    expectedSignPath: `/${config.bucket}/?list-type=2&max-keys=1`,
                    expectedHost: `s3.${config.region}.qiniucs.com`
                },
                {
                    name: '目录列表',
                    path: '?list-type=2&prefix=&delimiter=/',
                    expectedUrl: `https://s3.${config.region}.qiniucs.com/${config.bucket}/?list-type=2&prefix=&delimiter=/`,
                    expectedSignPath: `/${config.bucket}/?list-type=2&prefix=&delimiter=/`,
                    expectedHost: `s3.${config.region}.qiniucs.com`
                },
                {
                    name: '子目录列表',
                    path: '?list-type=2&prefix=videos/&delimiter=/',
                    expectedUrl: `https://s3.${config.region}.qiniucs.com/${config.bucket}/?list-type=2&prefix=videos/&delimiter=/`,
                    expectedSignPath: `/${config.bucket}/?list-type=2&prefix=videos/&delimiter=/`,
                    expectedHost: `s3.${config.region}.qiniucs.com`
                }
            ];

            let output = '';

            testCases.forEach(testCase => {
                // 模拟URL构建
                const actualUrl = `https://s3.${config.region}.qiniucs.com/${config.bucket}/${testCase.path}`;
                const actualSignPath = `/${config.bucket}/${testCase.path}`;
                const actualHost = `s3.${config.region}.qiniucs.com`;

                const urlMatch = actualUrl === testCase.expectedUrl;
                const pathMatch = actualSignPath === testCase.expectedSignPath;
                const hostMatch = actualHost === testCase.expectedHost;
                const allMatch = urlMatch && pathMatch && hostMatch;

                output += `
                    <div style="margin: 15px 0; padding: 10px; border: 1px solid ${allMatch ? '#c3e6cb' : '#f5c6cb'}; border-radius: 4px; background: ${allMatch ? '#d4edda' : '#f8d7da'};">
                        <h5>${testCase.name} ${allMatch ? '✅' : '❌'}</h5>
                        <p><strong>URL:</strong> ${urlMatch ? '✅' : '❌'} ${actualUrl}</p>
                        <p><strong>签名路径:</strong> ${pathMatch ? '✅' : '❌'} ${actualSignPath}</p>
                        <p><strong>Host头:</strong> ${hostMatch ? '✅' : '❌'} ${actualHost}</p>
                    </div>
                `;
            });

            document.getElementById('urlTests').innerHTML = output;
        }

        // 查询参数排序测试
        function testQueryParamSorting() {
            const testQuery = 'list-type=2&prefix=&delimiter=/';
            const sortedQuery = testQuery.split('&')
                .map(param => {
                    const [key, value = ''] = param.split('=');
                    return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
                })
                .sort()
                .join('&');

            console.log('查询参数排序测试:');
            console.log('原始:', testQuery);
            console.log('排序后:', sortedQuery);
            
            return sortedQuery;
        }

        // 页面加载时运行测试
        document.addEventListener('DOMContentLoaded', function() {
            testUrlBuilding();
            testQueryParamSorting();
            
            console.log('S3 Path-Style URL测试完成');
        });
    </script>
</body>
</html>
