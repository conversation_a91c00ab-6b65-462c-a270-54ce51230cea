<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3签名测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .title { font-weight: bold; color: #007acc; margin-bottom: 10px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .comparison-item { padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007acc; color: white; border: none; cursor: pointer; border-radius: 4px; padding: 8px 16px; margin: 5px; }
        button:hover { background: #005a9e; }
    </style>
</head>
<body>
    <div class="container">
        <h1>S3签名测试</h1>
        
        <div class="section">
            <div class="title">🔍 问题分析</div>
            <div class="comparison">
                <div class="comparison-item">
                    <h4 class="success">✅ 成功的请求 (连接测试)</h4>
                    <pre>
URL: https://siyuan-mediaplayer.s3.cn-south-1.qiniucs.com/?list-type=2&max-keys=1
Status: 200 OK
查询参数: list-type=2&max-keys=1
                    </pre>
                </div>
                <div class="comparison-item">
                    <h4 class="error">❌ 失败的请求 (获取目录)</h4>
                    <pre>
URL: https://siyuan-mediaplayer.s3.cn-south-1.qiniucs.com/?list-type=2&prefix=&delimiter=/
Status: 403 Forbidden
查询参数: list-type=2&prefix=&delimiter=/
                    </pre>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="title">🔧 修复内容</div>
            <ul>
                <li><strong>查询参数处理</strong>: 正确分离URI和查询字符串</li>
                <li><strong>签名生成</strong>: 确保查询参数包含在签名中</li>
                <li><strong>URL构建</strong>: 统一URL格式</li>
            </ul>
        </div>
        
        <div class="section">
            <div class="title">📊 签名生成测试</div>
            <button onclick="testSignatureGeneration()">测试签名生成</button>
            <div id="signatureTest" style="margin-top: 10px;"></div>
        </div>
        
        <div class="section">
            <div class="title">🎯 AWS4签名算法步骤</div>
            <ol>
                <li><strong>规范化请求</strong>: 构建canonical request</li>
                <li><strong>创建签名字符串</strong>: 包含时间戳和凭证范围</li>
                <li><strong>计算签名</strong>: 使用HMAC-SHA256</li>
                <li><strong>构建Authorization头</strong>: 包含签名信息</li>
            </ol>
        </div>
    </div>

    <script>
        async function sha256(message) {
            const hashBuffer = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(message));
            return Array.from(new Uint8Array(hashBuffer)).map(b => b.toString(16).padStart(2, '0')).join('');
        }

        async function hmacSha256(key, message) {
            const cryptoKey = await crypto.subtle.importKey('raw', key, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
            const signature = await crypto.subtle.sign('HMAC', cryptoKey, new TextEncoder().encode(message));
            return Array.from(new Uint8Array(signature)).map(b => b.toString(16).padStart(2, '0')).join('');
        }

        async function hmacSha256Raw(key, message) {
            const cryptoKey = await crypto.subtle.importKey('raw', key, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
            const signature = await crypto.subtle.sign('HMAC', cryptoKey, new TextEncoder().encode(message));
            return new Uint8Array(signature);
        }

        async function getSignatureKey(key, dateStamp, region) {
            const kDate = await hmacSha256Raw(new TextEncoder().encode('AWS4' + key), dateStamp);
            const kRegion = await hmacSha256Raw(kDate, region);
            const kService = await hmacSha256Raw(kRegion, 's3');
            return await hmacSha256Raw(kService, 'aws4_request');
        }

        async function generateS3Headers(method, path, config) {
            const now = new Date();
            const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
            const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
            const region = config.region || 'cn-south-1';

            const host = `${config.bucket}.s3.${region}.qiniucs.com`;
            
            // 正确处理查询参数
            const [uri, queryString] = path.startsWith('?') ? ['/', path.slice(1)] : [path, ''];
            const canonicalUri = uri;
            const canonicalQueryString = queryString;
            const canonicalHeaders = `host:${host}\nx-amz-content-sha256:e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\nx-amz-date:${amzDate}\n`;
            const signedHeaders = 'host;x-amz-content-sha256;x-amz-date';
            const canonicalRequest = `${method}\n${canonicalUri}\n${canonicalQueryString}\n${canonicalHeaders}\n${signedHeaders}\ne3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855`;
            
            const algorithm = 'AWS4-HMAC-SHA256';
            const credentialScope = `${dateStamp}/${region}/s3/aws4_request`;
            const stringToSign = `${algorithm}\n${amzDate}\n${credentialScope}\n${await sha256(canonicalRequest)}`;
            
            const signingKey = await getSignatureKey(config.secretKey, dateStamp, region);
            const signature = await hmacSha256(signingKey, stringToSign);
            
            return {
                headers: [
                    ['Authorization', `${algorithm} Credential=${config.accessKey}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`],
                    ['x-amz-content-sha256', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'],
                    ['x-amz-date', amzDate]
                ],
                debug: {
                    canonicalRequest,
                    stringToSign,
                    signature,
                    host,
                    canonicalUri,
                    canonicalQueryString
                }
            };
        }

        async function testSignatureGeneration() {
            const testConfig = {
                bucket: 'siyuan-mediaplayer',
                accessKey: 'test-access-key',
                secretKey: 'test-secret-key',
                region: 'cn-south-1'
            };

            const testCases = [
                {
                    name: '连接测试 (成功)',
                    path: '?list-type=2&max-keys=1'
                },
                {
                    name: '获取目录 (失败)',
                    path: '?list-type=2&prefix=&delimiter=/'
                }
            ];

            let output = '<h4>签名生成测试结果:</h4>';

            for (const testCase of testCases) {
                try {
                    const result = await generateS3Headers('GET', testCase.path, testConfig);
                    
                    output += `
                        <div style="margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <h5>${testCase.name}</h5>
                            <p><strong>路径:</strong> ${testCase.path}</p>
                            <p><strong>URI:</strong> ${result.debug.canonicalUri}</p>
                            <p><strong>查询字符串:</strong> ${result.debug.canonicalQueryString}</p>
                            <p><strong>主机:</strong> ${result.debug.host}</p>
                            <details>
                                <summary>详细信息</summary>
                                <pre>${JSON.stringify(result.debug, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } catch (error) {
                    output += `
                        <div style="margin: 15px 0; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; background: #f8d7da;">
                            <h5>${testCase.name} - 错误</h5>
                            <p style="color: #721c24;">${error.message}</p>
                        </div>
                    `;
                }
            }

            document.getElementById('signatureTest').innerHTML = output;
        }

        // 页面加载时自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('S3签名测试页面已加载');
        });
    </script>
</body>
</html>
