/**
 * S3存储媒体播放模块
 * 支持AWS S3、七牛云、阿里云OSS、腾讯云COS等S3兼容存储
 */
import type { MediaItem } from './types';
import { EXT } from './player';

// 接口定义
export interface S3Config {
    server: string;      // S3 endpoint
    accessKey: string;   // Access Key ID
    secretKey: string;   // Secret Access Key
    bucket: string;      // 存储桶名称
    region?: string;     // 区域
    connected?: boolean; // 连接状态
}

export interface S3File {
    name: string;
    path: string;
    size: number;
    is_dir: boolean;
    modified: string;
    etag?: string;
}

// 工具函数
const checkExt = (name: string, exts: string[]) => {
    const clean = name.toLowerCase().split('?')[0];
    return exts.some(ext => clean.endsWith(ext));
};

const media = {
    isAudioFile: (name: string) => checkExt(name, EXT.AUDIO),
    isMediaFile: (name: string) => checkExt(name, EXT.MEDIA),
    isSupported: (name: string) => checkExt(name, EXT.MEDIA)
};

/**
 * S3管理器
 */
export class S3Manager {
    private static config: S3Config | null = null;
    private static FILE_CACHE = new Map<string, {files: S3File[], timestamp: number}>();
    private static CACHE_EXPIRY = 5 * 60 * 1000; // 5分钟缓存

    // 基础方法
    private static checkConfig = () => {
        if (!this.config?.server || !this.config?.accessKey || !this.config?.secretKey || !this.config?.bucket) {
            throw new Error("S3未配置");
        }
    };

    private static buildUrl = (path: string) => {
        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
        return `${this.config!.server.replace(/\/$/, '')}/${this.config!.bucket}/${cleanPath}`;
    };

    /**
     * 连接验证
     */
    static async checkConnection(config: S3Config): Promise<{connected: boolean, message: string}> {
        try {
            // 简单的HEAD请求验证存储桶访问权限
            const testUrl = `${config.server.replace(/\/$/, '')}/${config.bucket}/`;
            const headers = await this.generateS3Headers('HEAD', '', config);
            
            const res = await fetch(testUrl, {
                method: 'HEAD',
                headers: Object.fromEntries(headers)
            });

            if (res.ok || res.status === 404) { // 404也表示能访问到存储桶
                this.config = {...config, connected: true};
                return {connected: true, message: "连接成功"};
            }
            return {connected: false, message: `连接失败: ${res.status} ${res.statusText}`};
        } catch (error) {
            return {connected: false, message: `连接失败: ${error instanceof Error ? error.message : String(error)}`};
        }
    }

    /**
     * 获取目录内容
     */
    static async getDirectoryContents(path = '/'): Promise<S3File[]> {
        this.checkConfig();
        const now = Date.now();
        
        // 检查缓存
        const cached = this.FILE_CACHE.get(path);
        if (cached && (now - cached.timestamp < this.CACHE_EXPIRY)) {
            return cached.files;
        }

        try {
            const prefix = path === '/' ? '' : path.replace(/^\//, '').replace(/\/$/, '') + '/';
            const listUrl = `${this.config!.server.replace(/\/$/, '')}/${this.config!.bucket}/?list-type=2&prefix=${encodeURIComponent(prefix)}&delimiter=/`;
            const headers = await this.generateS3Headers('GET', `/?list-type=2&prefix=${encodeURIComponent(prefix)}&delimiter=/`);
            
            const res = await fetch(listUrl, {
                method: 'GET',
                headers: Object.fromEntries(headers)
            });

            if (!res.ok) throw new Error(`获取文件列表失败: ${res.status}`);
            
            const xmlText = await res.text();
            const files = this.parseS3ListResponse(xmlText, path);
            
            this.FILE_CACHE.set(path, {files, timestamp: now});
            return files;
        } catch (error) {
            if (cached) return cached.files;
            throw error;
        }
    }

    /**
     * 解析S3列表响应
     */
    private static parseS3ListResponse(xmlText: string, basePath: string): S3File[] {
        try {
            const parser = new DOMParser();
            const doc = parser.parseFromString(xmlText, 'text/xml');
            const files: S3File[] = [];

            // 处理文件夹（CommonPrefixes）
            const prefixes = doc.querySelectorAll('CommonPrefixes Prefix');
            prefixes.forEach(prefix => {
                const fullPath = prefix.textContent || '';
                const name = fullPath.replace(/\/$/, '').split('/').pop() || '';
                if (name) {
                    files.push({
                        name,
                        path: '/' + fullPath,
                        size: 0,
                        is_dir: true,
                        modified: new Date().toISOString()
                    });
                }
            });

            // 处理文件（Contents）
            const contents = doc.querySelectorAll('Contents');
            contents.forEach(content => {
                const key = content.querySelector('Key')?.textContent || '';
                const size = parseInt(content.querySelector('Size')?.textContent || '0');
                const modified = content.querySelector('LastModified')?.textContent || '';
                const etag = content.querySelector('ETag')?.textContent || '';
                
                if (key && !key.endsWith('/')) {
                    const name = key.split('/').pop() || '';
                    if (name) {
                        files.push({
                            name,
                            path: '/' + key,
                            size,
                            is_dir: false,
                            modified,
                            etag: etag.replace(/"/g, '')
                        });
                    }
                }
            });

            return files;
        } catch (error) {
            console.error('解析S3响应失败:', error);
            return [];
        }
    }

    /**
     * 获取文件直接访问链接
     */
    static async getFileLink(path: string): Promise<string> {
        this.checkConfig();
        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
        
        // 生成预签名URL（简化版，实际使用中可能需要更复杂的签名）
        const url = `${this.config!.server.replace(/\/$/, '')}/${this.config!.bucket}/${cleanPath}`;
        return url;
    }

    /**
     * 创建媒体项
     */
    static async createMediaItemFromPath(path: string, timeParams: { startTime?: number, endTime?: number } = {}): Promise<MediaItem> {
        this.checkConfig();
        const fileName = path.split('/').pop() || '未知文件';
        const fileUrl = await this.getFileLink(path);
        const isAudio = media.isAudioFile(fileName);

        return {
            id: `s3-direct-${Date.now()}`,
            title: fileName,
            url: fileUrl,
            originalUrl: fileUrl,
            type: isAudio ? 'audio' : 'video',
            source: 's3',
            sourcePath: path,
            startTime: timeParams.startTime,
            endTime: timeParams.endTime,
            isLoop: timeParams.endTime !== undefined,
            thumbnail: isAudio ? '/plugins/siyuan-media-player/assets/images/audio.png' : '/plugins/siyuan-media-player/assets/images/video.png'
        };
    }

    /**
     * 处理S3媒体链接
     */
    static async handleS3MediaLink(url: string, timeParams: { startTime?: number, endTime?: number } = {}): Promise<{success: boolean; mediaItem?: MediaItem; error?: string}> {
        try {
            this.checkConfig();
            const s3Path = this.parsePathFromUrl(url);
            if (!s3Path) return {success: false, error: "无法从链接解析S3路径"};

            const mediaItem = await this.createMediaItemFromPath(s3Path, timeParams);
            return {success: true, mediaItem};
        } catch (error) {
            return {success: false, error: error instanceof Error ? error.message : String(error)};
        }
    }

    /**
     * 从URL解析S3路径
     */
    static parsePathFromUrl(url: string): string | null {
        try {
            if (!url || !media.isSupported(url)) return null;

            // 匹配S3 URL格式
            if (this.config?.server && url.startsWith(this.config.server)) {
                const bucketPrefix = `/${this.config.bucket}/`;
                const bucketIndex = url.indexOf(bucketPrefix);
                if (bucketIndex !== -1) {
                    return '/' + url.substring(bucketIndex + bucketPrefix.length).split(/[?#]/)[0];
                }
            }

            // 通用S3格式检测
            const patterns = [
                new RegExp(`https?://[^/]+/${this.config?.bucket || '[^/]+'}/(.*)`),
                /https?:\/\/([^.]+)\.s3\.([^.]+)\.amazonaws\.com\/(.*)/,
                /https?:\/\/s3\.([^.]+)\.amazonaws\.com\/([^/]+)\/(.*)/
            ];

            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match && match[match.length - 1]) {
                    return '/' + decodeURIComponent(match[match.length - 1].split(/[?#]/)[0]);
                }
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 创建目录媒体项列表
     */
    static async createMediaItemsFromDirectory(path = '/'): Promise<MediaItem[]> {
        this.checkConfig();
        const files = await this.getDirectoryContents(path);

        return files.map(file => {
            if (file.is_dir) {
                return {
                    id: `s3-folder-${Date.now()}-${Math.random().toString(36).slice(2,5)}`,
                    title: file.name,
                    type: 'folder',
                    url: '#',
                    source: 's3',
                    sourcePath: file.path,
                    is_dir: true,
                    thumbnail: '/plugins/siyuan-media-player/assets/images/folder.png'
                } as MediaItem;
            } else if (media.isMediaFile(file.name)) {
                return {
                    id: `s3-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
                    title: file.name,
                    url: this.buildUrl(file.path),
                    originalUrl: this.buildUrl(file.path),
                    thumbnail: media.isAudioFile(file.name) ? '/plugins/siyuan-media-player/assets/images/audio.png' : '/plugins/siyuan-media-player/assets/images/video.png',
                    type: media.isAudioFile(file.name) ? 'audio' : 'video',
                    source: 's3',
                    sourcePath: file.path
                } as MediaItem;
            }
            return null;
        }).filter(Boolean) as MediaItem[];
    }

    // 工具方法
    static getConfig = () => this.config;
    static setConfig = (config: S3Config) => { this.config = config; };
    static clearConnection = () => {
        this.config = null;
        this.FILE_CACHE.clear();
    };

    /**
     * 从配置初始化
     */
    static async initFromConfig(config: any): Promise<boolean> {
        const s3Config = config?.settings?.s3Config;
        if (!s3Config?.server || !s3Config?.accessKey || !s3Config?.secretKey || !s3Config?.bucket) return false;
        
        try {
            return (await this.checkConnection(s3Config)).connected;
        } catch {
            return false;
        }
    }

    /**
     * 生成S3签名头（简化版AWS4签名）
     */
    private static async generateS3Headers(method: string, path: string, config?: S3Config): Promise<[string, string][]> {
        const cfg = config || this.config!;
        const now = new Date();
        const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
        const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');

        // 简化的签名实现，实际生产环境可能需要完整的AWS4签名
        const auth = btoa(`${cfg.accessKey}:${cfg.secretKey}`);
        
        return [
            ['Authorization', `Basic ${auth}`],
            ['x-amz-date', amzDate],
            ['x-amz-content-sha256', 'UNSIGNED-PAYLOAD']
        ];
    }
}
