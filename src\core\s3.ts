/**
 * S3存储媒体播放模块
 * 支持AWS S3、七牛云、阿里云OSS、腾讯云COS等S3兼容存储
 */
import type { MediaItem } from './types';
import { EXT } from './player';

// 接口定义
export interface S3Config {
    server: string;      // S3 endpoint
    accessKey: string;   // Access Key ID
    secretKey: string;   // Secret Access Key
    bucket: string;      // 存储桶名称
    region?: string;     // 区域
    connected?: boolean; // 连接状态
}

export interface S3File {
    name: string;
    path: string;
    size: number;
    is_dir: boolean;
    modified: string;
    etag?: string;
}

// 工具函数
const checkExt = (name: string, exts: string[]) => {
    const clean = name.toLowerCase().split('?')[0];
    return exts.some(ext => clean.endsWith(ext));
};

const media = {
    isAudioFile: (name: string) => checkExt(name, EXT.AUDIO),
    isMediaFile: (name: string) => checkExt(name, EXT.MEDIA),
    isSupported: (name: string) => checkExt(name, EXT.MEDIA)
};

/**
 * S3管理器
 */
export class S3Manager {
    private static config: S3Config | null = null;
    private static FILE_CACHE = new Map<string, {files: S3File[], timestamp: number}>();
    private static CACHE_EXPIRY = 5 * 60 * 1000; // 5分钟缓存
    private static VERSION = 'v1.0.7-debug-signature'; // 版本标识

    // 基础方法
    private static checkConfig = () => {
        if (!this.config?.server || !this.config?.accessKey || !this.config?.secretKey || !this.config?.bucket) {
            throw new Error("S3未配置");
        }
    };

    private static buildUrl = (path: string) => {
        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
        // 使用Path Style格式（与tools中的forcePathStyle: true一致）
        const region = this.config!.region || 'cn-south-1';
        return `https://s3.${region}.qiniucs.com/${this.config!.bucket}/${cleanPath}`;
    };

    /**
     * 连接验证
     */
    static async checkConnection(config: S3Config): Promise<{connected: boolean, message: string}> {
        try {
            // 使用ListObjectsV2测试连接，更可靠
            const queryParams = '?list-type=2&max-keys=1';
            const region = config.region || 'cn-south-1';
            const url = `https://s3.${region}.qiniucs.com/${config.bucket}/${queryParams}`;

            const headers = await this.generateS3Headers('GET', `${config.bucket}/${queryParams}`, config);

            // 使用思源的代理API，与license.ts保持一致
            const res = await fetch('/api/network/forwardProxy', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    url,
                    method: 'GET',
                    timeout: 10000,
                    headers: headers.map(([key, value]) => ({ [key]: value }))
                })
            });

            const result = await res.json();
            if (result.code === 0) {
                this.config = {...config, connected: true};
                return {connected: true, message: "连接成功"};
            }

            // 检查是否有详细的错误信息
            let errorMsg = result.msg || '未知错误';
            if (result.data && result.data.body && result.data.body.includes('<Error>')) {
                // 解析S3错误响应
                try {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(result.data.body, 'text/xml');
                    const code = doc.querySelector('Code')?.textContent;
                    const message = doc.querySelector('Message')?.textContent;
                    if (code && message) {
                        errorMsg = `${code}: ${message}`;
                    }
                } catch (e) {
                    // 忽略解析错误，使用原始错误信息
                }
            }

            return {connected: false, message: `连接失败: ${errorMsg}`};
        } catch (error) {
            return {connected: false, message: `连接失败: ${error instanceof Error ? error.message : String(error)}`};
        }
    }

    /**
     * 获取目录内容
     */
    static async getDirectoryContents(path = '/'): Promise<S3File[]> {
        console.log('S3Manager版本:', this.VERSION);
        console.log('S3 getDirectoryContents 开始:', { path, config: !!this.config });

        this.checkConfig();
        const now = Date.now();

        // 检查缓存
        const cached = this.FILE_CACHE.get(path);
        if (cached && (now - cached.timestamp < this.CACHE_EXPIRY)) {
            console.log('使用缓存数据:', cached.files.length, '个文件');
            return cached.files;
        }

        try {
            const prefix = path === '/' ? '' : path.replace(/^\//, '').replace(/\/$/, '') + '/';
            const queryParams = `?list-type=2&prefix=${encodeURIComponent(prefix)}&delimiter=/`;
            const region = this.config!.region || 'cn-south-1';
            const url = `https://s3.${region}.qiniucs.com/${this.config!.bucket}/${queryParams}`;

            console.log('S3 getDirectoryContents:', { path, prefix, url });

            const headers = await this.generateS3Headers('GET', `${this.config!.bucket}/${queryParams}`);

            // 使用思源的代理API
            const requestBody = {
                url,
                method: 'GET',
                timeout: 10000,
                headers: headers.map(([key, value]) => ({ [key]: value }))
            };
            console.log('S3 发送请求:', requestBody);

            const res = await fetch('/api/network/forwardProxy', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            console.log('S3 响应状态:', res.status, res.statusText);

            if (!res.ok) {
                throw new Error(`HTTP请求失败: ${res.status} ${res.statusText}`);
            }

            const result = await res.json();
            console.log('S3 API响应:', result);

            if (result.code !== 0) throw new Error(`获取文件列表失败: ${result.msg || '未知错误'}`);

            if (!result.data || !result.data.body) {
                console.error('S3 API响应格式错误:', result);
                throw new Error(`API响应格式错误: data=${!!result.data}, body=${!!result.data?.body}`);
            }

            const files = this.parseS3ListResponse(result.data.body, path);

            this.FILE_CACHE.set(path, {files, timestamp: now});
            return files;
        } catch (error) {
            console.error('S3 getDirectoryContents 错误:', error);
            console.error('错误堆栈:', error.stack);
            if (cached) {
                console.log('使用缓存数据作为降级方案');
                return cached.files;
            }
            throw error;
        }
    }

    /**
     * 解析S3列表响应
     */
    private static parseS3ListResponse(xmlText: string, basePath: string): S3File[] {
        try {
            const parser = new DOMParser();
            const doc = parser.parseFromString(xmlText, 'text/xml');
            const files: S3File[] = [];

            // 处理文件夹（CommonPrefixes）
            const prefixes = doc.querySelectorAll('CommonPrefixes Prefix');
            prefixes.forEach(prefix => {
                const fullPath = prefix.textContent || '';
                const name = fullPath.replace(/\/$/, '').split('/').pop() || '';
                if (name) {
                    files.push({
                        name,
                        path: '/' + fullPath,
                        size: 0,
                        is_dir: true,
                        modified: new Date().toISOString()
                    });
                }
            });

            // 处理文件（Contents）
            const contents = doc.querySelectorAll('Contents');
            contents.forEach(content => {
                const key = content.querySelector('Key')?.textContent || '';
                const size = parseInt(content.querySelector('Size')?.textContent || '0');
                const modified = content.querySelector('LastModified')?.textContent || '';
                const etag = content.querySelector('ETag')?.textContent || '';
                
                if (key && !key.endsWith('/')) {
                    const name = key.split('/').pop() || '';
                    if (name) {
                        files.push({
                            name,
                            path: '/' + key,
                            size,
                            is_dir: false,
                            modified,
                            etag: etag.replace(/"/g, '')
                        });
                    }
                }
            });

            return files;
        } catch (error) {
            console.error('解析S3响应失败:', error);
            return [];
        }
    }

    /**
     * 获取文件直接访问链接
     */
    static async getFileLink(path: string): Promise<string> {
        this.checkConfig();
        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
        
        // 生成预签名URL（简化版，实际使用中可能需要更复杂的签名）
        const url = `${this.config!.server.replace(/\/$/, '')}/${this.config!.bucket}/${cleanPath}`;
        return url;
    }

    /**
     * 创建媒体项
     */
    static async createMediaItemFromPath(path: string, timeParams: { startTime?: number, endTime?: number } = {}): Promise<MediaItem> {
        this.checkConfig();
        const fileName = path.split('/').pop() || '未知文件';
        const fileUrl = await this.getFileLink(path);
        const isAudio = media.isAudioFile(fileName);

        return {
            id: `s3-direct-${Date.now()}`,
            title: fileName,
            url: fileUrl,
            originalUrl: fileUrl,
            type: isAudio ? 'audio' : 'video',
            source: 's3',
            sourcePath: path,
            startTime: timeParams.startTime,
            endTime: timeParams.endTime,
            isLoop: timeParams.endTime !== undefined,
            thumbnail: isAudio ? '/plugins/siyuan-media-player/assets/images/audio.png' : '/plugins/siyuan-media-player/assets/images/video.png'
        };
    }

    /**
     * 处理S3媒体链接
     */
    static async handleS3MediaLink(url: string, timeParams: { startTime?: number, endTime?: number } = {}): Promise<{success: boolean; mediaItem?: MediaItem; error?: string}> {
        try {
            this.checkConfig();
            const s3Path = this.parsePathFromUrl(url);
            if (!s3Path) return {success: false, error: "无法从链接解析S3路径"};

            const mediaItem = await this.createMediaItemFromPath(s3Path, timeParams);
            return {success: true, mediaItem};
        } catch (error) {
            return {success: false, error: error instanceof Error ? error.message : String(error)};
        }
    }

    /**
     * 从URL解析S3路径
     */
    static parsePathFromUrl(url: string): string | null {
        try {
            if (!url || !media.isSupported(url)) return null;

            // 匹配S3 URL格式
            if (this.config?.server && url.startsWith(this.config.server)) {
                const bucketPrefix = `/${this.config.bucket}/`;
                const bucketIndex = url.indexOf(bucketPrefix);
                if (bucketIndex !== -1) {
                    return '/' + url.substring(bucketIndex + bucketPrefix.length).split(/[?#]/)[0];
                }
            }

            // 通用S3格式检测
            const patterns = [
                new RegExp(`https?://[^/]+/${this.config?.bucket || '[^/]+'}/(.*)`),
                /https?:\/\/([^.]+)\.s3\.([^.]+)\.amazonaws\.com\/(.*)/,
                /https?:\/\/s3\.([^.]+)\.amazonaws\.com\/([^/]+)\/(.*)/
            ];

            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match && match[match.length - 1]) {
                    return '/' + decodeURIComponent(match[match.length - 1].split(/[?#]/)[0]);
                }
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 创建目录媒体项列表
     */
    static async createMediaItemsFromDirectory(path = '/'): Promise<MediaItem[]> {
        this.checkConfig();
        const files = await this.getDirectoryContents(path);

        console.log('S3 原始文件列表:', files);
        console.log('S3 文件数量:', files.length);

        const mediaItems = files.map(file => {
            console.log('S3 处理文件:', { name: file.name, is_dir: file.is_dir, isMedia: media.isMediaFile(file.name) });

            if (file.is_dir) {
                return {
                    id: `s3-folder-${Date.now()}-${Math.random().toString(36).slice(2,5)}`,
                    title: file.name,
                    type: 'folder',
                    url: '#',
                    source: 's3',
                    sourcePath: file.path,
                    is_dir: true,
                    thumbnail: '/plugins/siyuan-media-player/assets/images/folder.png'
                } as MediaItem;
            } else if (media.isMediaFile(file.name)) {
                return {
                    id: `s3-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
                    title: file.name,
                    url: this.buildUrl(file.path),
                    originalUrl: this.buildUrl(file.path),
                    thumbnail: media.isAudioFile(file.name) ? '/plugins/siyuan-media-player/assets/images/audio.png' : '/plugins/siyuan-media-player/assets/images/video.png',
                    type: media.isAudioFile(file.name) ? 'audio' : 'video',
                    source: 's3',
                    sourcePath: file.path
                } as MediaItem;
            }
            return null;
        }).filter(Boolean) as MediaItem[];

        console.log('S3 过滤后的媒体项:', mediaItems);
        return mediaItems;
    }

    // 工具方法
    static getConfig = () => this.config;
    static setConfig = (config: S3Config) => { this.config = config; };
    static clearConnection = () => {
        this.config = null;
        this.FILE_CACHE.clear();
    };

    /**
     * 从配置初始化
     */
    static async initFromConfig(config: any): Promise<boolean> {
        // 从s3Accounts数组中获取第一个可用的S3账号
        const s3Accounts = config?.settings?.s3Accounts || [];
        if (!s3Accounts.length) return false;

        // 尝试使用第一个S3账号
        const s3Account = s3Accounts[0];
        if (!s3Account?.server || !s3Account?.accessKey || !s3Account?.secretKey || !s3Account?.bucket) return false;

        try {
            const result = await this.checkConnection(s3Account);
            return result.connected;
        } catch {
            return false;
        }
    }

    /**
     * 生成S3签名头（AWS4签名）- 与license.ts保持一致
     */
    private static async generateS3Headers(method: string, path: string, config?: S3Config): Promise<[string, string][]> {
        const cfg = config || this.config!;
        const now = new Date();
        const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
        const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
        const region = cfg.region || 'cn-south-1';

        // 使用Path Style格式的主机名（与tools中的forcePathStyle: true一致）
        const host = `s3.${region}.qiniucs.com`;

        // 规范化请求 - 处理查询参数（AWS4签名要求）
        const [uri, queryString] = path.includes('?') ? path.split('?') : [path, ''];
        const canonicalUri = uri;

        // 对查询参数进行排序（AWS要求）
        const canonicalQueryString = queryString ?
            queryString.split('&')
                .map(param => {
                    const [key, value = ''] = param.split('=');
                    return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
                })
                .sort()
                .join('&') : '';

        const canonicalHeaders = `host:${host}\nx-amz-content-sha256:e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\nx-amz-date:${amzDate}\n`;
        const signedHeaders = 'host;x-amz-content-sha256;x-amz-date';
        const canonicalRequest = `${method}\n${canonicalUri}\n${canonicalQueryString}\n${canonicalHeaders}\n${signedHeaders}\ne3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855`;

        console.log('S3 签名调试:', {
            method,
            path,
            uri: canonicalUri,
            queryString: canonicalQueryString,
            host,
            canonicalRequest
        });

        // 创建签名字符串
        const algorithm = 'AWS4-HMAC-SHA256';
        const credentialScope = `${dateStamp}/${region}/s3/aws4_request`;
        const stringToSign = `${algorithm}\n${amzDate}\n${credentialScope}\n${await this.sha256(canonicalRequest)}`;

        // 计算签名
        const signingKey = await this.getSignatureKey(cfg.secretKey, dateStamp, region);
        const signature = await this.hmacSha256(signingKey, stringToSign);

        console.log('S3 签名详情:', {
            dateStamp,
            credentialScope,
            stringToSign,
            signature: signature.slice(0, 16) + '...'
        });

        return [
            ['Authorization', `${algorithm} Credential=${cfg.accessKey}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`],
            ['x-amz-content-sha256', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'],
            ['x-amz-date', amzDate]
        ];
    }

    // 辅助加密方法
    private static async sha256(message: string): Promise<string> {
        const hashBuffer = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(message));
        return Array.from(new Uint8Array(hashBuffer)).map(b => b.toString(16).padStart(2, '0')).join('');
    }

    private static async hmacSha256(key: Uint8Array, message: string): Promise<string> {
        const cryptoKey = await crypto.subtle.importKey('raw', key, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
        const signature = await crypto.subtle.sign('HMAC', cryptoKey, new TextEncoder().encode(message));
        return Array.from(new Uint8Array(signature)).map(b => b.toString(16).padStart(2, '0')).join('');
    }

    private static async getSignatureKey(key: string, dateStamp: string, region: string): Promise<Uint8Array> {
        const kDate = await this.hmacSha256Raw(new TextEncoder().encode('AWS4' + key), dateStamp);
        const kRegion = await this.hmacSha256Raw(kDate, region);
        const kService = await this.hmacSha256Raw(kRegion, 's3');
        return await this.hmacSha256Raw(kService, 'aws4_request');
    }

    private static async hmacSha256Raw(key: Uint8Array, message: string): Promise<Uint8Array> {
        const cryptoKey = await crypto.subtle.importKey('raw', key, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
        const signature = await crypto.subtle.sign('HMAC', cryptoKey, new TextEncoder().encode(message));
        return new Uint8Array(signature);
    }
}
