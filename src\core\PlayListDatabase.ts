import type { MediaItem } from './types';

/**
 * 播放列表数据库操作类
 * 负责所有与思源数据库的交互操作
 */
export class PlayListDatabase {
    private plugin: any;

    // 配置常量
    private readonly FIELDS = { 
        url: 'URL', duration: '时长', playlist: '所在标签', source: '来源', 
        type: '类型', artist: '艺术家', thumbnail: '封面图', 
        artistIcon: '艺术家头像', created: '创建时间' 
    };

    private readonly FIELD_DEFS = {
        source: { type: 'select', options: [['B站', '4'], ['本地', '6'], ['OpenList', '3'], ['WebDAV', '5']] },
        url: { type: 'url' }, artist: { type: 'text' }, artistIcon: { type: 'mAsset' }, 
        thumbnail: { type: 'mAsset' }, playlist: { type: 'mSelect', options: [['默认', '1']] }, 
        duration: { type: 'text' }, type: { type: 'select', options: [['视频', '4'], ['音频', '5']] }, 
        created: { type: 'date' }
    };

    constructor(plugin: any) {
        this.plugin = plugin;
    }

    // API调用
    private async api(path: string, data: any = {}) {
        return fetch(path, { 
            method: 'POST', 
            headers: { 'Content-Type': 'application/json' }, 
            body: JSON.stringify(data) 
        }).then(r => r.json());
    }

    // 字段映射工具
    private mapField(key: string, keyMap: any) {
        return Object.values(keyMap).find(k => k.desc === this.FIELDS[key]) || 
               keyMap[this.FIELDS[key]] || 
               Object.values(keyMap).find(k => k.name?.includes(this.FIELDS[key]) || this.FIELDS[key]?.includes(k.name));
    }

    private extractValue(value: any, key: string) {
        if (key === 'type') {
            return value?.mSelect?.[0]?.content === '音频' ? 'audio' : 'video';
        }
        return value?.text?.content || value?.url?.content || value?.mSelect?.[0]?.content || 
               value?.mAsset?.[0]?.content || value?.date?.content || '';
    }

    // 数据库基础操作
    private db = {
        getKeys: async (avId: string) => (await this.api('/api/av/getAttributeViewKeysByAvID', { avID: avId })).data || [],
        render: async (avId: string, viewId = '', page = 1, pageSize = -1) => (await this.api('/api/av/renderAttributeView', { id: avId, viewID: viewId, query: '', page, pageSize })).data || {},
        addKey: async (avId: string, keyID: string, keyName: string, keyType: string, keyIcon = '', previousKeyID = '') => this.api('/api/av/addAttributeViewKey', { avID: avId, keyID, keyName, keyType, keyIcon, previousKeyID }),
        setKeyDesc: async (avId: string, keyID: string, desc: string) => this.db.transaction([{ action: "setAttrViewColDesc", id: keyID, avID: avId, data: desc }]),
        transaction: async (transactions: any[]) => this.api('/api/transactions', { transactions }),
        addRow: async (avId: string, previousRowID = '') => this.api('/api/av/addAttributeViewRow', { avID: avId, previousRowID }),
        updateCell: async (avId: string, keyID: string, rowID: string, cellID: string, value: any) => this.api('/api/av/updateAttributeViewCell', { avID: avId, keyID, rowID, cellID, value }),
        removeRows: async (avId: string, rowIDs: string[]) => this.api('/api/av/removeAttributeViewRow', { avID: avId, rowIDs })
    };

    /**
     * 数据操作主函数
     */
    async dataOp(operation: string, params: any = {}) {
        const config = await this.plugin.loadData('config.json') || {};
        const avId = config.settings?.playlistDatabase;
        if (!avId && !['init'].includes(operation)) return;

        switch (operation) {
            case 'init':
                return this.initDatabase(params.avId);
            
            case 'ensure':
                return this.ensureTag(avId, params.tagName, params.description);
            
            case 'add':
                return this.addMedia(avId, params.media, params.playlist, params.checkDup);
            
            case 'load':
                return this.loadData(avId);
            
            case 'del':
                return this.deleteMedia(avId, params.title, params.tagName);
            
            case 'move':
                return this.moveMedia(avId, params.title, params.newPlaylist);
            
            case 'deleteTag':
                return this.deleteTag(avId, params.tagName);
            
            case 'renameTag':
                return this.renameTag(avId, params.oldName, params.newName);
        }
    }

    /**
     * 初始化数据库
     */
    private async initDatabase(avId: string) {
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        
        for (const [key, name] of Object.entries(this.FIELDS)) {
            const existingKey = this.mapField(key, keyMap);
            if (!existingKey) {
                const keyID = `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
                const fieldDef = this.FIELD_DEFS[key];
                await this.db.addKey(avId, keyID, name, fieldDef.type, '', '');
                if (fieldDef.options) {
                    const transactions = fieldDef.options.map(([optName, optId]) => ({
                        action: "addAttrViewColOption", id: keyID, avID: avId, data: { name: optName, color: "var(--b3-font-color8)" }
                    }));
                    await this.db.transaction(transactions);
                }
            } else if (!existingKey.desc) {
                await this.db.setKeyDesc(avId, existingKey.id, name);
            }
        }
    }

    /**
     * 确保标签存在
     */
    private async ensureTag(avId: string, tagName: string, description?: string) {
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        const playlistKey = this.mapField('playlist', keyMap);
        
        if (playlistKey && !playlistKey.options?.some(opt => opt.name === tagName)) {
            await this.db.transaction([{
                action: "addAttrViewColOption", 
                id: playlistKey.id, 
                avID: avId, 
                data: { name: tagName, color: "var(--b3-font-color8)" }
            }]);
        }
    }

    /**
     * 添加媒体项
     */
    private async addMedia(avId: string, media: MediaItem, playlist: string, checkDup: boolean) {
        const data = await this.db.render(avId);
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        
        if (checkDup) {
            const urlKey = this.mapField('url', keyMap);
            if (urlKey && data.rows?.some(row => this.extractValue(row.cells?.[urlKey.id], 'url') === media.url)) {
                throw new Error('媒体已存在');
            }
        }

        await this.ensureTag(avId, playlist);
        const row = await this.db.addRow(avId);
        const rowId = row.data.id;

        const updates = [
            { key: 'url', value: { url: { content: media.url } } },
            { key: 'playlist', value: { mSelect: [{ content: playlist, color: "var(--b3-font-color8)" }] } },
            { key: 'source', value: { mSelect: [{ content: media.source || '未知', color: "var(--b3-font-color8)" }] } },
            { key: 'type', value: { mSelect: [{ content: media.type === 'audio' ? '音频' : '视频', color: "var(--b3-font-color8)" }] } },
            { key: 'created', value: { date: { content: Date.now(), isNotEmpty: true } } }
        ];

        if (media.duration) updates.push({ key: 'duration', value: { text: { content: media.duration } } });
        if (media.artist) updates.push({ key: 'artist', value: { text: { content: media.artist } } });
        if (media.thumbnail) updates.push({ key: 'thumbnail', value: { mAsset: [{ content: media.thumbnail, name: "thumbnail", type: "image" }] } });
        if (media.artistIcon) updates.push({ key: 'artistIcon', value: { mAsset: [{ content: media.artistIcon, name: "artistIcon", type: "image" }] } });

        for (const { key, value } of updates) {
            const keyObj = this.mapField(key, keyMap);
            if (keyObj) {
                const cellId = data.rows?.find(r => r.id === rowId)?.cells?.[keyObj.id]?.id || `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
                await this.db.updateCell(avId, keyObj.id, rowId, cellId, value);
            }
        }
    }

    /**
     * 加载数据
     */
    private async loadData(avId: string) {
        const data = await this.db.render(avId);
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        const playlistKey = this.mapField('playlist', keyMap);
        
        const tags = [...(playlistKey?.options?.map(opt => opt.name) || []), '默认'].filter((t, i, a) => a.indexOf(t) === i);
        
        return {
            tags: ['目录', ...tags.filter(t => t !== '目录')],
            items: data.rows?.map(row => this.parseRowToMediaItem(row, keyMap)) || []
        };
    }

    /**
     * 解析行数据为媒体项
     */
    private parseRowToMediaItem(row: any, keyMap: any): MediaItem {
        const getValue = (key: string) => {
            const keyObj = this.mapField(key, keyMap);
            return keyObj ? this.extractValue(row.cells?.[keyObj.id], key) : '';
        };

        return {
            id: row.id,
            title: getValue('url')?.split('/').pop()?.split('?')[0] || '未知标题',
            url: getValue('url'),
            type: getValue('type') as 'audio' | 'video',
            source: getValue('source'),
            duration: getValue('duration'),
            artist: getValue('artist'),
            thumbnail: getValue('thumbnail'),
            artistIcon: getValue('artistIcon'),
            playlist: getValue('playlist'),
            created: getValue('created')
        };
    }

    /**
     * 删除媒体项
     */
    private async deleteMedia(avId: string, title?: string, tagName?: string) {
        const data = await this.db.render(avId);
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        
        let rowsToDelete = [];
        
        if (title) {
            const urlKey = this.mapField('url', keyMap);
            rowsToDelete = data.rows?.filter(row => {
                const url = this.extractValue(row.cells?.[urlKey.id], 'url');
                return url?.split('/').pop()?.split('?')[0] === title;
            }) || [];
        } else if (tagName) {
            const playlistKey = this.mapField('playlist', keyMap);
            rowsToDelete = data.rows?.filter(row => {
                const playlist = this.extractValue(row.cells?.[playlistKey.id], 'playlist');
                return playlist === tagName;
            }) || [];
        }
        
        if (rowsToDelete.length) {
            await this.db.removeRows(avId, rowsToDelete.map(r => r.id));
        }
    }

    /**
     * 移动媒体项
     */
    private async moveMedia(avId: string, title: string, newPlaylist: string) {
        await this.ensureTag(avId, newPlaylist);
        const data = await this.db.render(avId);
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        
        const urlKey = this.mapField('url', keyMap);
        const playlistKey = this.mapField('playlist', keyMap);
        
        const row = data.rows?.find(row => {
            const url = this.extractValue(row.cells?.[urlKey.id], 'url');
            return url?.split('/').pop()?.split('?')[0] === title;
        });
        
        if (row && playlistKey) {
            const cellId = row.cells?.[playlistKey.id]?.id || `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
            await this.db.updateCell(avId, playlistKey.id, row.id, cellId, {
                mSelect: [{ content: newPlaylist, color: "var(--b3-font-color8)" }]
            });
        }
    }

    /**
     * 删除标签
     */
    private async deleteTag(avId: string, tagName: string) {
        await this.deleteMedia(avId, undefined, tagName);
        
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        const playlistKey = this.mapField('playlist', keyMap);
        
        if (playlistKey) {
            const option = playlistKey.options?.find(opt => opt.name === tagName);
            if (option) {
                await this.db.transaction([{
                    action: "removeAttrViewColOption",
                    id: playlistKey.id,
                    avID: avId,
                    data: option.id
                }]);
            }
        }
    }

    /**
     * 重命名标签
     */
    private async renameTag(avId: string, oldName: string, newName: string) {
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        const playlistKey = this.mapField('playlist', keyMap);
        
        if (playlistKey) {
            const option = playlistKey.options?.find(opt => opt.name === oldName);
            if (option) {
                await this.db.transaction([{
                    action: "updateAttrViewColOption",
                    id: playlistKey.id,
                    avID: avId,
                    data: { ...option, name: newName }
                }]);
            }
        }
        
        // 更新所有使用该标签的媒体项
        const data = await this.db.render(avId);
        const rowsToUpdate = data.rows?.filter(row => {
            const playlist = this.extractValue(row.cells?.[playlistKey.id], 'playlist');
            return playlist === oldName;
        }) || [];
        
        for (const row of rowsToUpdate) {
            const cellId = row.cells?.[playlistKey.id]?.id || `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
            await this.db.updateCell(avId, playlistKey.id, row.id, cellId, {
                mSelect: [{ content: newName, color: "var(--b3-font-color8)" }]
            });
        }
    }

    // 公共API接口
    async load() { return this.dataOp('load'); }
    async add(url: string, playlist = '默认', checkDup = true) { 
        const Media = await import('./player');
        const result = await Media.Media.getMediaInfo(url);
        if (!result.success || !result.mediaItem) throw new Error(result.error || '无法解析媒体链接');
        return this.dataOp('add', { media: result.mediaItem, playlist, checkDup });
    }
    async delete(title?: string, tagName?: string) { return this.dataOp('del', { title, tagName }); }
    async move(title: string, newPlaylist: string) { return this.dataOp('move', { title, newPlaylist }); }
    async deleteTag(tagName: string) { return this.dataOp('deleteTag', { tagName }); }
    async renameTag(oldName: string, newName: string) { return this.dataOp('renameTag', { oldName, newName }); }
    async ensureTag(tagName: string, description?: string) { return this.dataOp('ensure', { tagName, description }); }
    async init(avId: string) { return this.dataOp('init', { avId }); }
}
