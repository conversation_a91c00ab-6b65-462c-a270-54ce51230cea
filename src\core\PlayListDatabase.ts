import type { MediaItem } from './types';

/**
 * 播放列表数据库操作类
 * 负责所有与思源数据库的交互操作
 */
export class PlayListDatabase {
    private plugin: any;

    // 配置常量
    private readonly FIELDS = {
        title: '媒体标题',
        url: 'URL', duration: '时长', playlist: '所在标签', source: '来源',
        type: '类型', artist: '艺术家', thumbnail: '封面图',
        artistIcon: '艺术家头像', created: '创建时间'
    };

    private readonly FIELD_DEFS = {
        title: { type: 'text' },
        source: { type: 'select', options: [['B站', '4'], ['本地', '6'], ['OpenList', '3'], ['WebDAV', '5']] },
        url: { type: 'url' }, artist: { type: 'text' }, artistIcon: { type: 'mAsset' },
        thumbnail: { type: 'mAsset' }, playlist: { type: 'mSelect', options: [['默认', '1']] },
        duration: { type: 'text' }, type: { type: 'select', options: [['视频', '4'], ['音频', '5']] },
        created: { type: 'date' }
    };

    constructor(plugin: any) {
        this.plugin = plugin;
    }

    // API调用
    private async api(path: string, data: any = {}) {
        return fetch(path, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        }).then(r => r.json());
    }

    // 获取数据库ID
    private async getAvId(id: string) {
        if (!id || !/^\d{14}-[a-z0-9]{7}$/.test(id)) {
            throw new Error('请输入有效的数据库ID');
        }
        const result = await this.api('/api/query/sql', {
            stmt: `SELECT markdown FROM blocks WHERE type='av' AND id='${id}'`
        }).catch(() => ({ data: [] }));
        const avId = result.data?.[0]?.markdown?.match(/data-av-id="([^"]+)"/)?.[1];
        return avId || id;
    }

    // 字段映射工具
    private mapField(key: string, keyMap: any) {
        const fieldName = this.FIELDS[key];
        if (!fieldName) return null;

        // 1. 优先通过desc匹配（最准确）
        const byDesc = Object.values(keyMap).find((k: any) => k.desc === fieldName);
        if (byDesc) return byDesc;

        // 2. 通过name直接匹配
        const byName = keyMap[fieldName];
        if (byName) return byName;

        // 3. 通过name包含关系匹配
        const byNameContains = Object.values(keyMap).find((k: any) =>
            k.name === fieldName ||
            k.name?.includes(fieldName) ||
            fieldName?.includes(k.name)
        );
        if (byNameContains) return byNameContains;

        return null;
    }

    private extractValue(value: any, key: string) {
        if (key === 'type') {
            return value?.mSelect?.[0]?.content === '音频' ? 'audio' : 'video';
        }
        if (key === 'playlist') {
            // 对于playlist字段，返回所有标签的数组
            return value?.mSelect?.map(item => item.content) || [];
        }
        return value?.text?.content || value?.url?.content || value?.mSelect?.[0]?.content ||
               value?.mAsset?.[0]?.content || value?.date?.content || '';
    }

    // 确保字段选项存在
    private async ensureFieldOptions(avId: string, keyID: string, options: string[][]) {
        const before = (await this.db.render(avId)).view?.rows?.length || 0;
        for (const [name, color] of options) {
            await this.db.addRow(avId, [{
                keyID,
                id: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
                blockID: '',
                type: 'mSelect',
                mSelect: [{ content: name, color }]
            }]);
        }
        const after = (await this.db.render(avId)).view?.rows || [];
        if (after.length > before) {
            await this.db.removeRows(avId, after.slice(before).map(r => r.id));
        }
    }

    // 创建字段值
    private createValue(key: string, value: any, keyData?: any) {
        const v = String(value);
        const base = {
            keyID: keyData?.id,
            id: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
            blockID: '',
            type: keyData?.type || 'text'
        };
        const color = (name: string) => keyData?.options?.find(o => o.name === name)?.color || '1';

        const creators = {
            url: () => ({ ...base, type: 'url', url: { content: v } }),
            playlist: () => ({ ...base, type: 'mSelect', mSelect: Array.isArray(value) ? value.map(i => ({ content: String(i), color: color(String(i)) })) : [{ content: v, color: color(v) }] }),
            source: () => ({ ...base, type: 'select', mSelect: [{ content: v, color: color(v) }] }),
            type: () => ({ ...base, type: 'select', mSelect: [{ content: v, color: color(v) }] }),
            artistIcon: () => ({ ...base, type: 'mAsset', mAsset: [{ type: 'image', name: '', content: v }] }),
            thumbnail: () => ({ ...base, type: 'mAsset', mAsset: [{ type: 'image', name: '', content: v }] }),
            created: () => ({ ...base, type: 'date', date: { content: value, isNotEmpty: true, hasEndDate: false, isNotTime: false } })
        };

        return creators[key] || (() => ({ ...base, text: { content: v } }));
    }

    // 数据库基础操作
    private db = {
        getKeys: async (avId: string) => (await this.api('/api/av/getAttributeViewKeysByAvID', { avID: avId })).data || [],
        render: async (avId: string, viewId = '', page = 1, pageSize = -1) => (await this.api('/api/av/renderAttributeView', { id: avId, viewID: viewId, query: '', page, pageSize })).data || {},
        addKey: async (avId: string, keyID: string, keyName: string, keyType: string, keyIcon = '', previousKeyID = '') => this.api('/api/av/addAttributeViewKey', { avID: avId, keyID, keyName, keyType, keyIcon, previousKeyID }),
        setKeyDesc: async (avId: string, keyID: string, desc: string) => this.db.transaction([{ action: "setAttrViewColDesc", id: keyID, avID: avId, data: desc }]),
        removeKey: async (avId: string, keyID: string) => this.api('/api/av/removeAttributeViewKey', { avID: avId, keyID }),
        updateFieldName: async (avId: string, keyID: string, newName: string, oldName: string, keyType: string) => this.db.transaction([{ action: "updateAttrViewColName", id: keyID, avID: avId, data: { name: newName } }]),
        transaction: async (operations: any[], undoOperations: any[] = []) => this.api('/api/transactions', {
            transactions: [{ doOperations: operations, undoOperations }],
            session: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
            app: "qd1f",
            reqId: Date.now()
        }),
        addRow: async (avId: string, values: any[]) => this.api('/api/av/appendAttributeViewDetachedBlocksWithValues', { avID: avId, blocksValues: [values] }),
        updateCell: async (avId: string, keyID: string, rowID: string, cellID: string, value: any) => this.api('/api/av/updateAttributeViewCell', { avID: avId, keyID, rowID, cellID, value }),
        removeRows: async (avId: string, rowIDs: string[]) => this.api('/api/av/removeAttributeViewBlocks', { avID: avId, srcIDs: rowIDs }),
        deleteTagOption: async (avId: string, keyID: string, tagName: string, options: any[]) => {
            if (!options.some(opt => opt.name === tagName)) throw new Error('标签选项不存在');
            return this.api('/api/transactions', {
                transactions: [{
                    doOperations: [
                        { action: "removeAttrViewColOption", id: keyID, avID: avId, data: tagName },
                        { action: "doUpdateUpdated", id: avId.replace(/-[^-]+$/, '-2vkgxt0'), data: new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14) }
                    ],
                    undoOperations: [{ action: "updateAttrViewColOptions", id: keyID, avID: avId, data: options }]
                }],
                session: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
                app: "qd1f",
                reqId: Date.now()
            });
        },
        renameTagOption: async (avId: string, keyID: string, oldName: string, newName: string, options: any[]) => {
            const newOptions = options.map(opt => opt.name === oldName ? { ...opt, name: newName } : opt);
            return this.db.transaction([{ action: "updateAttrViewColOptions", id: keyID, avID: avId, data: newOptions }]);
        },
        sortTags: async (avId: string, keyID: string, sortedOptions: any[]) => {
            return this.db.transaction([{ action: "updateAttrViewColOptions", id: keyID, avID: avId, data: sortedOptions }]);
        },
        sortItem: async (avId: string, rowID: string, previousRowID: string) => {
            return this.api('/api/av/sortAttributeViewRow', { avID: avId, rowID, previousRowID });
        }
    };

    /**
     * 数据操作主函数
     */
    async dataOp(operation: string, params: any = {}) {
        const config = await this.plugin.loadData('config.json') || {};
        let avId = config.settings?.playlistDb?.avId || config.settings?.playlistDb?.id;

        // 对于init操作，使用传入的avId
        if (operation === 'init' && params.avId) {
            avId = params.avId;
        }

        if (!avId && !['init'].includes(operation)) return;

        switch (operation) {
            case 'init':
                return this.initDatabase(params.avId);
            
            case 'ensure':
                const ensureKeys = await this.db.getKeys(avId);
                const ensureKeyMap = Object.fromEntries(ensureKeys.map(k => [k.name, k]));
                const ensurePlaylistKey = this.mapField('playlist', ensureKeyMap);
                if (ensurePlaylistKey && !ensurePlaylistKey.options?.some(opt => opt.name === params.tagName)) {
                    // 使用旧组件的方法：创建临时行来确保选项存在
                    const before = (await this.db.render(avId)).view?.rows?.length || 0;
                    await this.db.addRow(avId, [this.createValue('playlist', [params.tagName], ensurePlaylistKey)()]);
                    const after = (await this.db.render(avId)).view?.rows || [];
                    if (after.length > before) {
                        await this.db.removeRows(avId, after.slice(before).map(r => r.id));
                    }
                }
                return;
            
            case 'add':
                return this.addMedia(avId, params.media, params.playlist, params.checkDup);
            
            case 'load':
                return this.loadData(avId);
            
            case 'del':
                return this.deleteMedia(avId, params.title, params.tagName);
            
            case 'move':
                return this.moveMedia(avId, params.title, params.newPlaylist);
            
            case 'deleteTag':
                await this.deleteMedia(avId, undefined, params.tagName);
                const deleteKeys = await this.db.getKeys(avId);
                const deleteKeyMap = Object.fromEntries(deleteKeys.map(k => [k.name, k]));
                const deletePlaylistKey = this.mapField('playlist', deleteKeyMap);
                if (deletePlaylistKey?.options) {
                    await this.db.deleteTagOption(avId, deletePlaylistKey.id, params.tagName, deletePlaylistKey.options);
                }
                return;

            case 'renameTag':
                const renameKeys = await this.db.getKeys(avId);
                const renameKeyMap = Object.fromEntries(renameKeys.map(k => [k.name, k]));
                const renamePlaylistKey = this.mapField('playlist', renameKeyMap);
                if (renamePlaylistKey?.options) {
                    await this.db.renameTagOption(avId, renamePlaylistKey.id, params.oldName, params.newName, renamePlaylistKey.options);
                }
                return;
        }
    }

    /**
     * 初始化数据库
     */
    private async initDatabase(dbId: string) {
        const avId = await this.getAvId(dbId);
        const keys = await this.db.getKeys(avId);

        // 清理旧的无用字段
        await Promise.all(keys.filter(k =>
            k.type === 'select' &&
            !Object.values(this.FIELDS).includes(k.name) &&
            k.name !== '标题' &&
            k.name !== '主键'
        ).map(field => this.db.removeKey(avId, field.id)));

        // 获取主键作为起始位置，并确保主键有正确的描述
        const updatedKeys = await this.db.getKeys(avId);
        const primaryKey = updatedKeys.find(k => k.name === '主键' || k.name === '标题');
        if (primaryKey && !primaryKey.desc) {
            await this.db.setKeyDesc(avId, primaryKey.id, '主键');
        }
        let previousKeyID = primaryKey?.id || '';

        // 统一处理所有字段（包括媒体标题）
        for (const [key, name] of Object.entries(this.FIELDS)) {
            const existingKey = updatedKeys.find(k => k.name === name || k.desc === name);
            if (!existingKey) {
                const fieldDef = this.FIELD_DEFS[key] || { type: 'text' };
                const keyID = `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
                await this.db.addKey(avId, keyID, name, fieldDef.type, '', previousKeyID);
                await this.db.setKeyDesc(avId, keyID, name);
                if (fieldDef.options) {
                    await this.ensureFieldOptions(avId, keyID, fieldDef.options);
                }
                previousKeyID = keyID;
            } else if (!existingKey.desc) {
                await this.db.setKeyDesc(avId, existingKey.id, name);
            }
        }
    }



    /**
     * 添加媒体项
     */
    private async addMedia(avId: string, media: MediaItem, playlist: string, checkDup: boolean) {
        if (checkDup && media.url) {
            const data = await this.db.render(avId);
            const keys = await this.db.getKeys(avId);
            const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
            const urlKey = this.mapField('url', keyMap);

            const exists = data.view?.rows?.find(row =>
                row.cells?.find(c => c.value?.keyID === urlKey?.id)?.value?.url?.content === media.url
            );
            if (exists) throw new Error('媒体已存在');
        }

        // 处理图片资源
        try {
            const { imageToLocalAsset } = await import('../core/document');
            if (media.thumbnail) media.thumbnail = await imageToLocalAsset(media.thumbnail);
            if (media.artistIcon) media.artistIcon = await imageToLocalAsset(media.artistIcon);
        } catch (e) {
            // 如果图片处理失败，继续执行
        }

        // 获取字段映射
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        const values = [];

        // 必须为主键字段提供值（思源数据库要求）
        const primaryKey = keys.find(k => k.name === '主键' || k.name === '标题');
        if (primaryKey && media.title) {
            values.push({
                keyID: primaryKey.id,
                id: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
                blockID: '',
                type: 'block',
                block: {
                    id: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
                    content: media.title,
                    created: Date.now(),
                    updated: Date.now()
                },
                isDetached: true
            });
        }



        // 处理所有字段（包括媒体标题字段）
        Object.entries(this.FIELDS).forEach(([key]) => {
            const keyData = this.mapField(key, keyMap);
            if (!keyData) return;

            let v = media[key];
            if (key === 'title') {
                v = media.title;
            } else if (key === 'source') {
                v = media.source === 'openlist' ? 'OpenList' :
                    media.source === 'webdav' ? 'WebDAV' :
                    (media.url?.includes('bilibili.com') || media.bvid) ? 'B站' :
                    (media.source === 'local' || media.url?.startsWith('file://')) ? '本地' : '普通';
            } else if (key === 'playlist') {
                v = [playlist];
            } else if (key === 'type') {
                v = media.type === 'audio' ? '音频' : '视频';
            } else if (key === 'created') {
                v = Date.now();
            }

            if (v !== undefined && v !== null && v !== '') {
                const value = this.createValue(key, v, keyData)();
                if (value) values.push(value);
            }
        });

        // 调试：检查是否有keyID冲突
        const keyIDs = values.map(v => v.keyID);
        const uniqueKeyIDs = [...new Set(keyIDs)];
        if (keyIDs.length !== uniqueKeyIDs.length) {
            console.error('发现keyID冲突:', keyIDs);
            console.error('重复的keyID:', keyIDs.filter((id, index) => keyIDs.indexOf(id) !== index));
        }

        // 调试：检查媒体标题字段的keyID
        const titleValue = values.find(v => v.text?.content);
        if (titleValue) {
            console.log('媒体标题字段keyID:', titleValue.keyID);
            const keys = await this.db.getKeys(avId);
            const primaryKey = keys.find(k => k.name === '主键' || k.name === '标题');
            if (primaryKey) {
                console.log('主键字段keyID:', primaryKey.id);
                if (titleValue.keyID === primaryKey.id) {
                    console.error('错误：媒体标题使用了主键字段的keyID！');
                }
            }
        }

        console.log(`准备添加 ${values.length} 个字段值到数据库`);
        const result = await this.db.addRow(avId, values);
        console.log('添加结果:', result);
        return result;
    }

    /**
     * 加载数据
     */
    private async loadData(avId: string) {
        const data = await this.db.render(avId);
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        const playlistKey = this.mapField('playlist', keyMap);

        const tags = [...(playlistKey?.options?.map(opt => opt.name) || []), '默认'].filter((t, i, a) => a.indexOf(t) === i);

        // 使用正确的数据结构
        const rows = data.view?.rows || [];
        const allItems = rows.map(row => this.parseRowToMediaItem(row, keyMap));

        return {
            tags: ['目录', ...tags.filter(t => t !== '目录')],
            items: allItems
        };
    }

    /**
     * 解析行数据为媒体项
     */
    private parseRowToMediaItem(row: any, keyMap: any): MediaItem {
        const getValue = (key: string) => {
            const keyObj = this.mapField(key, keyMap);
            return keyObj ? this.extractValue(row.cells?.[keyObj.id], key) : '';
        };

        // 获取标题，优先从媒体标题字段读取
        const titleFromField = getValue('title');
        const titleFromUrl = getValue('url')?.split('/').pop()?.split('?')[0];
        const title = titleFromField || titleFromUrl || '未知标题';

        return {
            id: row.id,
            title: title,
            url: getValue('url'),
            type: getValue('type') as 'audio' | 'video',
            source: getValue('source'),
            duration: getValue('duration'),
            artist: getValue('artist'),
            thumbnail: getValue('thumbnail'),
            artistIcon: getValue('artistIcon'),
            playlist: getValue('playlist'),
            created: getValue('created')
        };
    }

    /**
     * 删除媒体项
     */
    private async deleteMedia(avId: string, title?: string, tagName?: string) {
        const data = await this.db.render(avId);
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        
        let rowsToDelete = [];
        
        if (title) {
            const titleKey = this.mapField('title', keyMap);
            const urlKey = this.mapField('url', keyMap);
            rowsToDelete = data.view?.rows?.filter(row => {
                const titleValue = this.extractValue(row.cells?.[titleKey?.id], 'title');
                const urlValue = this.extractValue(row.cells?.[urlKey?.id], 'url');
                const titleFromUrl = urlValue?.split('/').pop()?.split('?')[0];
                return titleValue === title || titleFromUrl === title;
            }) || [];
        } else if (tagName) {
            const playlistKey = this.mapField('playlist', keyMap);
            rowsToDelete = data.view?.rows?.filter(row => {
                const playlist = this.extractValue(row.cells?.[playlistKey?.id], 'playlist');
                return Array.isArray(playlist) ? playlist.includes(tagName) : playlist === tagName;
            }) || [];
        }
        
        if (rowsToDelete.length) {
            await this.db.removeRows(avId, rowsToDelete.map(r => r.id));
        }
    }

    /**
     * 移动媒体项
     */
    private async moveMedia(avId: string, title: string, newPlaylist: string) {
        await this.ensureTag(avId, newPlaylist);
        const data = await this.db.render(avId);
        const keys = await this.db.getKeys(avId);
        const keyMap = Object.fromEntries(keys.map(k => [k.name, k]));
        
        const titleKey = this.mapField('title', keyMap);
        const urlKey = this.mapField('url', keyMap);
        const playlistKey = this.mapField('playlist', keyMap);

        const row = data.view?.rows?.find(row => {
            const titleValue = this.extractValue(row.cells?.[titleKey?.id], 'title');
            const urlValue = this.extractValue(row.cells?.[urlKey?.id], 'url');
            const titleFromUrl = urlValue?.split('/').pop()?.split('?')[0];
            return titleValue === title || titleFromUrl === title;
        });
        
        if (row && playlistKey) {
            const cellId = row.cells?.[playlistKey.id]?.id || `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
            await this.db.updateCell(avId, playlistKey.id, row.id, cellId, {
                mSelect: [{ content: newPlaylist, color: "var(--b3-font-color8)" }]
            });
        }
    }





    // 公共API接口
    async load() { return this.dataOp('load'); }
    async add(url: string, playlist = '默认', checkDup = true) { 
        const Media = await import('./player');
        const result = await Media.Media.getMediaInfo(url);
        if (!result.success || !result.mediaItem) throw new Error(result.error || '无法解析媒体链接');
        return this.dataOp('add', { media: result.mediaItem, playlist, checkDup });
    }
    async delete(title?: string, tagName?: string) { return this.dataOp('del', { title, tagName }); }
    async move(title: string, newPlaylist: string) { return this.dataOp('move', { title, newPlaylist }); }
    async deleteTag(tagName: string) { return this.dataOp('deleteTag', { tagName }); }
    async renameTag(oldName: string, newName: string) { return this.dataOp('renameTag', { oldName, newName }); }
    async ensureTag(tagName: string, description?: string) { return this.dataOp('ensure', { tagName, description }); }
    async init(dbId: string) {
        // 获取真正的avId并保存到配置，保持与设置组件一致的格式
        const avId = await this.getAvId(dbId);
        const config = await this.plugin.loadData('config.json') || {};
        if (!config.settings) config.settings = {};

        // 保存为设置组件期望的格式：{ id: 用户输入的ID, avId: 真正的avId }
        config.settings.playlistDb = { id: dbId, avId: avId };
        await this.plugin.saveData('config.json', config, 2);

        return this.dataOp('init', { avId });
    }
}
