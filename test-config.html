<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置结构测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .config-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .config-title { font-weight: bold; color: #007acc; margin-bottom: 10px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>配置结构测试</h1>
        
        <div class="config-section">
            <div class="config-title">期望的配置结构:</div>
            <pre id="expectedConfig"></pre>
        </div>
        
        <div class="config-section">
            <div class="config-title">测试结果:</div>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        // 模拟期望的配置结构
        const expectedConfig = {
            settings: {
                // S3账号数组
                s3Accounts: [
                    {
                        id: "s3-account-1",
                        server: "https://s3.cn-south-1.qiniucs.com",
                        accessKey: "your-access-key",
                        secretKey: "your-secret-key",
                        bucket: "your-bucket-name",
                        region: "cn-south-1"
                    }
                ],
                // WebDAV账号数组
                webdavAccounts: [
                    {
                        id: "webdav-account-1",
                        server: "https://your-webdav-server.com",
                        username: "your-username",
                        password: "your-password"
                    }
                ],
                // OpenList账号数组
                openlistAccounts: [
                    {
                        id: "openlist-account-1",
                        server: "https://your-openlist-server.com",
                        username: "your-username",
                        password: "your-password"
                    }
                ]
            }
        };

        // 显示期望的配置结构
        document.getElementById('expectedConfig').textContent = JSON.stringify(expectedConfig, null, 2);

        // 测试配置读取逻辑
        function testConfigReading() {
            const results = [];

            // 测试S3配置读取
            const s3Accounts = expectedConfig?.settings?.s3Accounts || [];
            if (s3Accounts.length > 0) {
                const s3Account = s3Accounts[0];
                const s3Valid = s3Account?.server && s3Account?.accessKey && s3Account?.secretKey && s3Account?.bucket;
                results.push(`<div class="${s3Valid ? 'success' : 'error'}">S3配置: ${s3Valid ? '✅ 有效' : '❌ 无效'}</div>`);
                if (s3Valid) {
                    results.push(`<div class="info">  - 服务器: ${s3Account.server}</div>`);
                    results.push(`<div class="info">  - 存储桶: ${s3Account.bucket}</div>`);
                }
            } else {
                results.push(`<div class="error">S3配置: ❌ 未找到账号</div>`);
            }

            // 测试WebDAV配置读取
            const webdavAccounts = expectedConfig?.settings?.webdavAccounts || [];
            if (webdavAccounts.length > 0) {
                const webdavAccount = webdavAccounts[0];
                const webdavValid = webdavAccount?.server && webdavAccount?.username && webdavAccount?.password;
                results.push(`<div class="${webdavValid ? 'success' : 'error'}">WebDAV配置: ${webdavValid ? '✅ 有效' : '❌ 无效'}</div>`);
                if (webdavValid) {
                    results.push(`<div class="info">  - 服务器: ${webdavAccount.server}</div>`);
                    results.push(`<div class="info">  - 用户名: ${webdavAccount.username}</div>`);
                }
            } else {
                results.push(`<div class="error">WebDAV配置: ❌ 未找到账号</div>`);
            }

            // 测试OpenList配置读取
            const openlistAccounts = expectedConfig?.settings?.openlistAccounts || [];
            if (openlistAccounts.length > 0) {
                const openlistAccount = openlistAccounts[0];
                const openlistValid = openlistAccount?.server && openlistAccount?.username && openlistAccount?.password;
                results.push(`<div class="${openlistValid ? 'success' : 'error'}">OpenList配置: ${openlistValid ? '✅ 有效' : '❌ 无效'}</div>`);
                if (openlistValid) {
                    results.push(`<div class="info">  - 服务器: ${openlistAccount.server}</div>`);
                    results.push(`<div class="info">  - 用户名: ${openlistAccount.username}</div>`);
                }
            } else {
                results.push(`<div class="error">OpenList配置: ❌ 未找到账号</div>`);
            }

            return results.join('');
        }

        // 显示测试结果
        document.getElementById('testResults').innerHTML = testConfigReading();

        // 模拟空配置测试
        function testEmptyConfig() {
            const emptyConfig = { settings: {} };
            
            console.log('测试空配置:');
            console.log('S3账号数量:', (emptyConfig?.settings?.s3Accounts || []).length);
            console.log('WebDAV账号数量:', (emptyConfig?.settings?.webdavAccounts || []).length);
            console.log('OpenList账号数量:', (emptyConfig?.settings?.openlistAccounts || []).length);
        }

        testEmptyConfig();
    </script>
</body>
</html>
