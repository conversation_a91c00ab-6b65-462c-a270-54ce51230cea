# S3存储集成功能

## 概述

本插件新增了S3存储支持，可以直接浏览和播放存储在S3兼容存储服务中的媒体文件。支持AWS S3、七牛云、阿里云OSS、腾讯云COS、MinIO等S3兼容存储。

## 功能特性

- ✅ **多云存储支持**: 支持AWS S3、七牛云、阿里云OSS、腾讯云COS、MinIO等
- ✅ **文件浏览**: 支持目录树导航，文件夹和媒体文件展示
- ✅ **媒体播放**: 直接播放S3中的音频和视频文件
- ✅ **缓存机制**: 5分钟文件列表缓存，提升浏览体验
- ✅ **安全认证**: 支持AWS4签名认证（简化版）
- ✅ **多账号管理**: 支持配置多个S3存储账号

## 配置方法

### 1. 添加S3账号

1. 打开插件设置页面
2. 切换到"账号"标签页
3. 点击"添加账号"按钮
4. 选择"S3存储"
5. 填写以下信息：
   - **服务器地址**: S3 endpoint URL (如: `https://s3.amazonaws.com`)
   - **访问密钥**: Access Key ID
   - **秘密密钥**: Secret Access Key  
   - **存储桶名称**: Bucket name

### 2. 常见S3服务配置示例

#### AWS S3
```
服务器地址: https://s3.amazonaws.com
访问密钥: AKIA...
秘密密钥: your-secret-key
存储桶名称: your-bucket-name
```

#### 七牛云
```
服务器地址: https://s3.cn-south-1.qiniucs.com
访问密钥: your-access-key
秘密密钥: your-secret-key
存储桶名称: your-bucket-name
```

#### 阿里云OSS
```
服务器地址: https://oss-cn-hangzhou.aliyuncs.com
访问密钥: LTAI...
秘密密钥: your-secret-key
存储桶名称: your-bucket-name
```

#### 腾讯云COS
```
服务器地址: https://cos.ap-beijing.myqcloud.com
访问密钥: AKID...
秘密密钥: your-secret-key
存储桶名称: your-bucket-name
```

#### MinIO
```
服务器地址: http://localhost:9000
访问密钥: minioadmin
秘密密钥: minioadmin
存储桶名称: your-bucket-name
```

## 使用方法

### 1. 浏览S3文件

1. 在播放列表中点击"+"按钮
2. 选择"浏览S3存储"
3. 系统会自动创建"S3存储"标签页
4. 浏览文件夹和媒体文件

### 2. 播放媒体文件

- 点击媒体文件即可直接播放
- 支持音频和视频格式
- 支持时间戳播放和循环播放

### 3. 路径导航

- 支持面包屑导航
- 点击路径中的任意部分快速跳转
- 支持返回上级目录

## 支持的媒体格式

### 音频格式
- MP3, WAV, FLAC, AAC, OGG, M4A, WMA

### 视频格式  
- MP4, AVI, MKV, MOV, WMV, FLV, WEBM, M4V

## 技术实现

### 核心模块
- `src/core/s3.ts`: S3存储核心功能
- `src/components/Setting.svelte`: 设置界面S3账号管理
- `src/components/PlayList.svelte`: 播放列表S3集成

### 主要功能
1. **连接验证**: 通过HEAD请求验证存储桶访问权限
2. **文件列表**: 使用ListObjectsV2 API获取文件和文件夹
3. **媒体播放**: 生成直接访问链接用于播放
4. **缓存管理**: 文件列表缓存机制，减少API调用

### 安全特性
- 密钥信息加密存储
- 支持AWS4签名认证
- 安全的API调用封装

## 故障排除

### 连接失败
1. 检查服务器地址是否正确
2. 验证访问密钥和秘密密钥
3. 确认存储桶名称和权限
4. 检查网络连接和CORS设置

### 文件列表为空
1. 确认存储桶中有文件
2. 检查访问权限设置
3. 验证路径格式是否正确

### 播放失败
1. 确认文件格式是否支持
2. 检查文件访问权限
3. 验证网络连接稳定性

## 开发说明

### 扩展新的S3服务
1. 在`S3Manager.generateS3Headers`中添加特定服务的签名逻辑
2. 更新`parsePathFromUrl`方法支持新的URL格式
3. 在设置界面添加预设配置

### 自定义功能
- 可以扩展支持更多S3 API功能
- 支持自定义缓存策略
- 可以添加文件上传功能

## 注意事项

1. **网络要求**: 需要稳定的网络连接访问S3服务
2. **权限设置**: 确保S3账号有足够的读取权限
3. **CORS配置**: 某些S3服务可能需要配置CORS策略
4. **费用考虑**: 频繁的API调用可能产生费用，建议合理使用缓存
5. **安全性**: 妥善保管访问密钥，避免泄露

## 更新日志

### v1.0.0
- ✅ 基础S3存储支持
- ✅ 多云存储提供商兼容
- ✅ 文件浏览和媒体播放
- ✅ 账号管理和设置界面
- ✅ 缓存机制和错误处理
