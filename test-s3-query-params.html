<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3查询参数签名测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .title { font-weight: bold; color: #007acc; margin-bottom: 10px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        button { background: #007acc; color: white; border: none; cursor: pointer; border-radius: 4px; padding: 8px 16px; margin: 5px; }
        button:hover { background: #005a9e; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .comparison-item { padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>S3查询参数签名测试</h1>
        
        <div class="section">
            <div class="title">🔍 问题分析</div>
            <p>AWS4签名要求查询参数必须按字母顺序排序，并且正确编码。</p>
            <div class="comparison">
                <div class="comparison-item">
                    <h4 class="error">❌ 问题查询参数</h4>
                    <pre>list-type=2&prefix=&delimiter=/</pre>
                    <p>问题：参数顺序可能不正确，空值处理有问题</p>
                </div>
                <div class="comparison-item">
                    <h4 class="success">✅ 修复后查询参数</h4>
                    <pre>delimiter=%2F&list-type=2&prefix=</pre>
                    <p>修复：按字母顺序排序，正确URL编码</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="title">🔧 查询参数处理测试</div>
            <button onclick="testQueryParamProcessing()">测试查询参数处理</button>
            <div id="queryTest" style="margin-top: 10px;"></div>
        </div>
        
        <div class="section">
            <div class="title">📊 AWS4签名规范</div>
            <ul>
                <li><strong>查询参数排序</strong>: 按参数名字母顺序排序</li>
                <li><strong>URL编码</strong>: 参数名和值都需要URL编码</li>
                <li><strong>空值处理</strong>: 空值保留但不编码</li>
                <li><strong>分隔符</strong>: 使用&连接参数</li>
            </ul>
        </div>
    </div>

    <script>
        function testQueryParamProcessing() {
            const testCases = [
                {
                    name: '连接测试查询参数',
                    input: 'list-type=2&max-keys=1',
                    expected: 'list-type=2&max-keys=1'
                },
                {
                    name: '目录列表查询参数（问题）',
                    input: 'list-type=2&prefix=&delimiter=/',
                    expected: 'delimiter=%2F&list-type=2&prefix='
                },
                {
                    name: '子目录查询参数',
                    input: 'list-type=2&prefix=folder/&delimiter=/',
                    expected: 'delimiter=%2F&list-type=2&prefix=folder%2F'
                }
            ];

            let output = '<h4>查询参数处理测试结果:</h4>';

            testCases.forEach(testCase => {
                const processed = processQueryParams(testCase.input);
                const isCorrect = processed === testCase.expected;
                
                output += `
                    <div style="margin: 15px 0; padding: 10px; border: 1px solid ${isCorrect ? '#c3e6cb' : '#f5c6cb'}; border-radius: 4px; background: ${isCorrect ? '#d4edda' : '#f8d7da'};">
                        <h5>${testCase.name} ${isCorrect ? '✅' : '❌'}</h5>
                        <p><strong>输入:</strong> ${testCase.input}</p>
                        <p><strong>处理结果:</strong> ${processed}</p>
                        <p><strong>期望结果:</strong> ${testCase.expected}</p>
                        ${!isCorrect ? `<p style="color: #721c24;"><strong>状态:</strong> 不匹配</p>` : ''}
                    </div>
                `;
            });

            document.getElementById('queryTest').innerHTML = output;
        }

        function processQueryParams(queryString) {
            if (!queryString) return '';
            
            return queryString.split('&')
                .map(param => {
                    const [key, value = ''] = param.split('=');
                    return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
                })
                .sort()
                .join('&');
        }

        // 完整的签名生成测试
        async function sha256(message) {
            const hashBuffer = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(message));
            return Array.from(new Uint8Array(hashBuffer)).map(b => b.toString(16).padStart(2, '0')).join('');
        }

        async function testFullSignature() {
            const testConfig = {
                bucket: 'siyuan-playlist',
                accessKey: 'test-key',
                secretKey: 'test-secret',
                region: 'cn-south-1'
            };

            const now = new Date();
            const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
            const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
            const host = `${testConfig.bucket}.s3.${testConfig.region}.qiniucs.com`;

            // 测试问题查询参数
            const problemQuery = 'list-type=2&prefix=&delimiter=/';
            const [uri, queryString] = ['/', problemQuery];
            
            const canonicalQueryString = queryString ? 
                queryString.split('&')
                    .map(param => {
                        const [key, value = ''] = param.split('=');
                        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
                    })
                    .sort()
                    .join('&') : '';

            const canonicalHeaders = `host:${host}\nx-amz-content-sha256:e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\nx-amz-date:${amzDate}\n`;
            const signedHeaders = 'host;x-amz-content-sha256;x-amz-date';
            const canonicalRequest = `GET\n${uri}\n${canonicalQueryString}\n${canonicalHeaders}\n${signedHeaders}\ne3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855`;

            console.log('完整签名测试:');
            console.log('原始查询:', problemQuery);
            console.log('规范化查询:', canonicalQueryString);
            console.log('规范化请求:');
            console.log(canonicalRequest);

            const requestHash = await sha256(canonicalRequest);
            console.log('请求哈希:', requestHash);

            return {
                originalQuery: problemQuery,
                canonicalQuery: canonicalQueryString,
                canonicalRequest,
                requestHash
            };
        }

        // 页面加载时运行测试
        document.addEventListener('DOMContentLoaded', function() {
            testQueryParamProcessing();
            testFullSignature().then(result => {
                console.log('签名测试完成:', result);
            });
        });
    </script>
</body>
</html>
