<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3修复后测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .title { font-weight: bold; color: #007acc; margin-bottom: 10px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .comparison-item { padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>S3修复后测试</h1>
        
        <div class="section">
            <div class="title">🔧 修复内容对比</div>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>修复前 (有问题)</h4>
                    <pre>
URL格式: 
https://bucket.s3.cn-south-1.qiniucs.com/

请求方式: 
直接fetch请求

签名算法: 
自定义AWS4实现
                    </pre>
                </div>
                <div class="comparison-item">
                    <h4>修复后 (与license.ts一致)</h4>
                    <pre>
URL格式: 
https://bucket.s3.cn-south-1.qiniucs.com/

请求方式: 
/api/network/forwardProxy

签名算法: 
与license.ts完全一致
                    </pre>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="title">📋 关键修复点</div>
            <ul>
                <li><strong>URL构建</strong>: 使用与license.ts相同的格式 <code>https://bucket.s3.region.qiniucs.com/</code></li>
                <li><strong>请求代理</strong>: 使用思源的 <code>/api/network/forwardProxy</code> API</li>
                <li><strong>签名算法</strong>: 完全复制license.ts的AWS4签名实现</li>
                <li><strong>错误处理</strong>: 统一的错误响应格式处理</li>
            </ul>
        </div>
        
        <div class="section">
            <div class="title">🎯 测试步骤</div>
            <ol>
                <li>确保你的S3账号配置正确</li>
                <li>在播放列表中点击"+"按钮</li>
                <li>选择"浏览S3存储"</li>
                <li>系统应该能够成功连接并列出文件</li>
            </ol>
        </div>
        
        <div class="section">
            <div class="title">🔍 调试信息</div>
            <div id="debugInfo">
                <p>如果仍然遇到问题，请检查以下内容：</p>
                <ul>
                    <li>确认存储桶名称正确</li>
                    <li>确认Access Key和Secret Key有效</li>
                    <li>确认存储桶在cn-south-1区域</li>
                    <li>检查浏览器控制台的详细错误信息</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <div class="title">📊 License.ts vs S3.ts 实现对比</div>
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="background: #f8f9fa;">
                    <th style="border: 1px solid #ddd; padding: 8px;">功能</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">License.ts</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">S3.ts (修复后)</th>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">URL格式</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">bucket.s3.region.qiniucs.com</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">✅ 相同</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">请求方式</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">/api/network/forwardProxy</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">✅ 相同</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">签名算法</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">AWS4-HMAC-SHA256</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">✅ 相同</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">Content-SHA256</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">✅ 相同</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">默认区域</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">cn-south-1</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">✅ 相同</td>
                </tr>
            </table>
        </div>
    </div>

    <script>
        // 显示当前时间
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date().toLocaleString();
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML += `<p><strong>测试时间:</strong> ${now}</p>`;
        });
        
        // 模拟签名生成测试
        async function testSignature() {
            const testConfig = {
                bucket: 'siyuan-mediaplayer',
                accessKey: 'test-key',
                secretKey: 'test-secret',
                region: 'cn-south-1'
            };
            
            const now = new Date();
            const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
            const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
            
            const host = `${testConfig.bucket}.s3.${testConfig.region}.qiniucs.com`;
            const canonicalUri = '/?list-type=2&max-keys=1';
            
            console.log('测试签名生成:');
            console.log('Host:', host);
            console.log('URI:', canonicalUri);
            console.log('Date:', amzDate);
            
            return {
                host,
                canonicalUri,
                amzDate,
                dateStamp
            };
        }
        
        // 运行测试
        testSignature().then(result => {
            console.log('签名测试结果:', result);
        });
    </script>
</body>
</html>
